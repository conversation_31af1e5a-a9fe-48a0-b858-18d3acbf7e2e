import tkinter as tk
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure

class LayoutTest:
    def __init__(self):
        self.radius = 100
        self.angle1 = 30
        self.angle2 = 150
        self.height = 50
        self.time_progress = 0
        
        self.setup_gui()
        
    def setup_gui(self):
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("布局测试")
        self.root.geometry("1000x700")
        
        # 创建主框架
        main_frame = tk.Frame(self.root, bg='white')
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建matplotlib图形框架 - 固定高度
        plot_frame = tk.Frame(main_frame, bg='yellow', height=400)
        plot_frame.pack(side=tk.TOP, fill=tk.X)
        plot_frame.pack_propagate(False)  # 防止子组件改变框架大小
        
        # 创建matplotlib图形
        self.fig = Figure(figsize=(10, 4))
        self.canvas = FigureCanvasTkAgg(self.fig, master=plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 创建一个简单的子图
        self.ax = self.fig.add_subplot(111)
        
        # 创建控制面板框架 - 固定高度
        control_main_frame = tk.Frame(main_frame, bg='lightgreen', height=200)
        control_main_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=5)
        control_main_frame.pack_propagate(False)  # 防止子组件改变框架大小
        
        # 创建控制面板
        self.create_control_panel(control_main_frame)
        
        # 初始化图形
        self.update_plot()
        
    def create_control_panel(self, parent_frame):
        # 添加标题
        title_label = tk.Label(parent_frame, text="🎛️ 控制面板测试 🎛️", 
                             font=("Arial", 14, "bold"), bg='lightgreen', fg='darkgreen')
        title_label.pack(pady=5)
        
        # 控制面板框架
        control_frame = tk.Frame(parent_frame, bg='lightgreen')
        control_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 第一行控制
        row1 = tk.Frame(control_frame, bg='lightgreen')
        row1.pack(fill=tk.X, pady=5)
        
        # 半径控制
        tk.Label(row1, text="半径:", bg='lightgreen', font=("Arial", 10)).pack(side=tk.LEFT)
        self.radius_scale = tk.Scale(row1, from_=50, to=300, orient=tk.HORIZONTAL,
                                   command=self.update_radius, length=200, bg='lightgreen')
        self.radius_scale.set(self.radius)
        self.radius_scale.pack(side=tk.LEFT, padx=10)
        
        # 角度控制
        tk.Label(row1, text="角度:", bg='lightgreen', font=("Arial", 10)).pack(side=tk.LEFT, padx=(20,0))
        self.angle_scale = tk.Scale(row1, from_=0, to=360, orient=tk.HORIZONTAL,
                                  command=self.update_angle, length=200, bg='lightgreen')
        self.angle_scale.set(self.angle1)
        self.angle_scale.pack(side=tk.LEFT, padx=10)
        
        # 第二行控制
        row2 = tk.Frame(control_frame, bg='lightgreen')
        row2.pack(fill=tk.X, pady=5)
        
        # 按钮
        test_btn = tk.Button(row2, text="测试按钮", command=self.test_button, 
                           font=("Arial", 10), bg='white')
        test_btn.pack(side=tk.LEFT, padx=10)
        
        reset_btn = tk.Button(row2, text="重置", command=self.reset_values,
                            font=("Arial", 10), bg='white')
        reset_btn.pack(side=tk.LEFT, padx=10)
        
        # 状态标签
        self.status_label = tk.Label(row2, text="状态: 就绪", bg='lightgreen', 
                                   font=("Arial", 10), fg='darkgreen')
        self.status_label.pack(side=tk.LEFT, padx=20)
        
        print("控制面板创建完成")
        
    def update_radius(self, val):
        self.radius = float(val)
        self.status_label.config(text=f"半径: {self.radius}")
        self.update_plot()
        
    def update_angle(self, val):
        self.angle1 = float(val)
        self.status_label.config(text=f"角度: {self.angle1}")
        self.update_plot()
        
    def test_button(self):
        self.status_label.config(text="按钮被点击!")
        print("测试按钮被点击")
        
    def reset_values(self):
        self.radius = 100
        self.angle1 = 30
        self.radius_scale.set(self.radius)
        self.angle_scale.set(self.angle1)
        self.status_label.config(text="已重置")
        self.update_plot()
        
    def update_plot(self):
        try:
            self.ax.clear()
            
            # 绘制简单的圆和点
            circle = plt.Circle((0, 0), self.radius/100, fill=False, color='blue')
            self.ax.add_patch(circle)
            
            x = (self.radius/100) * np.cos(np.radians(self.angle1))
            y = (self.radius/100) * np.sin(np.radians(self.angle1))
            self.ax.plot(x, y, 'ro', markersize=8)
            
            self.ax.set_xlim(-3, 3)
            self.ax.set_ylim(-3, 3)
            self.ax.set_aspect('equal')
            self.ax.grid(True)
            self.ax.set_title(f'测试图 - 半径:{self.radius}, 角度:{self.angle1}')
            
            self.canvas.draw()
            
        except Exception as e:
            print(f"更新图形时出错: {e}")
            
    def run(self):
        print("启动布局测试...")
        self.root.mainloop()

if __name__ == "__main__":
    test = LayoutTest()
    test.run()
