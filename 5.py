import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import Slider, Button
import matplotlib.animation as animation
import math
from matplotlib.patches import Circle
import tkinter as tk
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure



#显示中文
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class UAVTransmissionVisualizer:
    def __init__(self):
        # 系统参数
        self.uav_speed = 20  # m/s
        self.fc = 2.4e9      # 载波频率 Hz
        self.Pm = 1.0        # 地面用户发射功率 W
        self.Pu = 1.0        # 无人机发射功率 W
        self.Bgt = 1e6       # 地面用户带宽 Hz
        self.Bu = 1e6        # 无人机带宽 Hz
        self.sigma_squared = 1e-12  # 噪声功率 W
        
        # 初始参数
        self.radius = 100
        self.angle1 = 30
        self.angle2 = 150
        self.uav_height = 50
        self.time_progress = 0
        
        # 动画相关
        self.is_animating = False
        self.animation_speed = 0.01
        
        self.setup_gui()
        
    def calculate_uav_transmission_rates(self, h_u, d_m, f_c=2.4e9, 
                                       P_m=1.0, P_u=1.0, B_gt=1e6, B_u=1e6, 
                                       sigma_squared=1e-12, c=3e8, 
                                       a=9.61, b=0.16, zeta_los=1.0, zeta_nlos=20.0):
        """
        计算无人机与地面用户的上下行链路传输速率
        """
        # 计算俯仰角 (度)
        theta_m = math.asin(h_u / d_m) * 180 / math.pi
        
        # 计算LoS概率
        P_r_los = 1 / (1 + a * math.exp(-b * (theta_m - a)))
        P_r_nlos = 1 - P_r_los
        
        # 计算基本路径损耗 (dB)
        basic_path_loss = 20 * math.log10(4 * math.pi * f_c * d_m / c)
        
        # 计算LoS和NLoS路径损耗 (dB)
        PL_los = basic_path_loss + zeta_los
        PL_nlos = basic_path_loss + zeta_nlos
        
        # 计算平均路径损耗 (dB)
        PL_m = P_r_los * PL_los + P_r_nlos * PL_nlos
        
        # 将路径损耗转换为线性值
        path_loss_linear = 10 ** (-PL_m / 10)
        
        # 计算上行链路传输速率 (bps)
        uplink_snr = (P_m * path_loss_linear) / sigma_squared
        uplink_rate = B_gt * math.log2(1 + uplink_snr)
        
        # 计算下行链路传输速率 (bps)
        downlink_snr = (P_u * path_loss_linear) / sigma_squared
        downlink_rate = B_u * math.log2(1 + downlink_snr)
        
        return {
            'uplink_rate': uplink_rate,
            'downlink_rate': downlink_rate,
            'path_loss_db': PL_m,
            'los_probability': P_r_los,
            'nlos_probability': P_r_nlos,
            'pitch_angle': theta_m
        }
    
    def setup_gui(self):
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("无人机传输速率实时可视化系统")
        self.root.geometry("1400x800")
        
        # 创建matplotlib图形
        self.fig = Figure(figsize=(14, 8))
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.root)
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)
        
        # 创建子图
        self.ax1 = self.fig.add_subplot(221)  # 主视图
        self.ax2 = self.fig.add_subplot(222)  # 传输速率图
        self.ax3 = self.fig.add_subplot(223)  # 参数随时间变化
        self.ax4 = self.fig.add_subplot(224)  # 路径损耗图
        
        # 创建控制面板
        self.create_control_panel()
        
        # 初始化图形
        self.update_plots()
        
    def create_control_panel(self):
        # 控制面板框架
        control_frame = tk.Frame(self.root)
        control_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)
        
        # 第一行控制
        row1 = tk.Frame(control_frame)
        row1.pack(fill=tk.X, pady=2)
        
        # 半径控制
        tk.Label(row1, text="圆形半径 (m):").pack(side=tk.LEFT)
        self.radius_scale = tk.Scale(row1, from_=50, to=300, orient=tk.HORIZONTAL, 
                                   command=self.update_radius, length=150)
        self.radius_scale.set(self.radius)
        self.radius_scale.pack(side=tk.LEFT, padx=5)
        
        # 点1角度控制
        tk.Label(row1, text="点1角度 (°):").pack(side=tk.LEFT, padx=(20,0))
        self.angle1_scale = tk.Scale(row1, from_=0, to=360, orient=tk.HORIZONTAL, 
                                   command=self.update_angle1, length=150)
        self.angle1_scale.set(self.angle1)
        self.angle1_scale.pack(side=tk.LEFT, padx=5)
        
        # 第二行控制
        row2 = tk.Frame(control_frame)
        row2.pack(fill=tk.X, pady=2)
        
        # 点2角度控制
        tk.Label(row2, text="点2角度 (°):").pack(side=tk.LEFT)
        self.angle2_scale = tk.Scale(row2, from_=0, to=360, orient=tk.HORIZONTAL, 
                                   command=self.update_angle2, length=150)
        self.angle2_scale.set(self.angle2)
        self.angle2_scale.pack(side=tk.LEFT, padx=5)
        
        # 无人机高度控制
        tk.Label(row2, text="无人机高度 (m):").pack(side=tk.LEFT, padx=(20,0))
        self.height_scale = tk.Scale(row2, from_=10, to=200, orient=tk.HORIZONTAL, 
                                   command=self.update_height, length=150)
        self.height_scale.set(self.uav_height)
        self.height_scale.pack(side=tk.LEFT, padx=5)
        
        # 第三行控制
        row3 = tk.Frame(control_frame)
        row3.pack(fill=tk.X, pady=2)
        
        # 时间进度控制
        tk.Label(row3, text="时间进度:").pack(side=tk.LEFT)
        self.time_scale = tk.Scale(row3, from_=0, to=1, orient=tk.HORIZONTAL, 
                                 command=self.update_time, length=300, resolution=0.01)
        self.time_scale.set(self.time_progress)
        self.time_scale.pack(side=tk.LEFT, padx=5)
        
        # 动画控制按钮
        self.animate_button = tk.Button(row3, text="开始动画", command=self.toggle_animation)
        self.animate_button.pack(side=tk.LEFT, padx=10)
        
        # 重置按钮
        reset_button = tk.Button(row3, text="重置", command=self.reset_parameters)
        reset_button.pack(side=tk.LEFT, padx=5)
        
    def update_radius(self, val):
        self.radius = float(val)
        self.update_plots()
        
    def update_angle1(self, val):
        self.angle1 = float(val)
        self.update_plots()
        
    def update_angle2(self, val):
        self.angle2 = float(val)
        self.update_plots()
        
    def update_height(self, val):
        self.uav_height = float(val)
        self.update_plots()
        
    def update_time(self, val):
        self.time_progress = float(val)
        self.update_plots()
        
    def toggle_animation(self):
        if not self.is_animating:
            self.is_animating = True
            self.animate_button.config(text="停止动画")
            self.animate()
        else:
            self.is_animating = False
            self.animate_button.config(text="开始动画")
            
    def animate(self):
        if self.is_animating:
            self.time_progress += self.animation_speed
            if self.time_progress >= 1:
                self.time_progress = 0
            self.time_scale.set(self.time_progress)
            self.update_plots()
            self.root.after(50, self.animate)
            
    def reset_parameters(self):
        self.radius = 100
        self.angle1 = 30
        self.angle2 = 150
        self.uav_height = 50
        self.time_progress = 0
        
        self.radius_scale.set(self.radius)
        self.angle1_scale.set(self.angle1)
        self.angle2_scale.set(self.angle2)
        self.height_scale.set(self.uav_height)
        self.time_scale.set(self.time_progress)
        
        self.update_plots()
        
    def update_plots(self):
        # 清除所有子图
        for ax in [self.ax1, self.ax2, self.ax3, self.ax4]:
            ax.clear()
            
        # 计算点的位置
        point1 = np.array([self.radius * np.cos(np.radians(self.angle1)),
                          self.radius * np.sin(np.radians(self.angle1))])
        point2 = np.array([self.radius * np.cos(np.radians(self.angle2)),
                          self.radius * np.sin(np.radians(self.angle2))])
        
        # 计算当前无人机位置
        current_pos = point1 + self.time_progress * (point2 - point1)
        
        # 计算传输参数
        distance_from_center = np.linalg.norm(current_pos)
        d_m = np.sqrt(distance_from_center**2 + self.uav_height**2)
        
        rates = self.calculate_uav_transmission_rates(
            self.uav_height, d_m, self.fc, self.Pm, self.Pu, 
            self.Bgt, self.Bu, self.sigma_squared
        )
        
        # 绘制主视图
        self.plot_main_view(point1, point2, current_pos)
        
        # 绘制传输速率图
        self.plot_transmission_rates(rates)
        
        # 绘制参数随时间变化
        self.plot_parameter_trends()
        
        # 绘制路径损耗图
        self.plot_path_loss_analysis()
        
        # 更新画布
        self.canvas.draw()
        
    def plot_main_view(self, point1, point2, current_pos):
        # 绘制圆形边界
        circle = Circle((0, 0), self.radius, fill=False, color='gray', linestyle='--')
        self.ax1.add_patch(circle)
        
        # 绘制圆心（用户位置）
        self.ax1.plot(0, 0, 'ro', markersize=8, label='用户位置')
        
        # 绘制起始点和终点
        self.ax1.plot(point1[0], point1[1], 'bo', markersize=6, label='点1')
        self.ax1.plot(point2[0], point2[1], 'go', markersize=6, label='点2')
        
        # 绘制连接线
        self.ax1.plot([point1[0], point2[0]], [point1[1], point2[1]], 'k-', linewidth=2)
        
        # 绘制当前无人机位置
        self.ax1.plot(current_pos[0], current_pos[1], 'o', color='orange', markersize=8, label='无人机')
        
        # 绘制从圆心到无人机的连线
        self.ax1.plot([0, current_pos[0]], [0, current_pos[1]], 'r--', alpha=0.7)
        
        # 设置坐标轴
        limit = self.radius * 1.2
        self.ax1.set_xlim(-limit, limit)
        self.ax1.set_ylim(-limit, limit)
        self.ax1.set_aspect('equal')
        self.ax1.grid(True, alpha=0.3)
        self.ax1.set_xlabel('距离 (m)')
        self.ax1.set_ylabel('距离 (m)')
        self.ax1.set_title('无人机飞行路径')
        self.ax1.legend()
        
    def plot_transmission_rates(self, rates):
        # 绘制传输速率柱状图
        categories = ['上行速率', '下行速率']
        values = [rates['uplink_rate']/1e6, rates['downlink_rate']/1e6]
        colors = ['blue', 'red']
        
        bars = self.ax2.bar(categories, values, color=colors, alpha=0.7)
        self.ax2.set_ylabel('速率 (Mbps)')
        self.ax2.set_title('传输速率')
        self.ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            self.ax2.text(bar.get_x() + bar.get_width()/2., height,
                         f'{value:.2f}', ha='center', va='bottom')
                         
        # 添加参数信息
        info_text = f"路径损耗: {rates['path_loss_db']:.1f} dB\n"
        info_text += f"LoS概率: {rates['los_probability']:.3f}\n"
        info_text += f"俯仰角: {rates['pitch_angle']:.1f}°"
        
        self.ax2.text(0.02, 0.98, info_text, transform=self.ax2.transAxes,
                     verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
    def plot_parameter_trends(self):
        # 计算整个路径上的参数变化
        time_points = np.linspace(0, 1, 50)
        uplink_rates = []
        downlink_rates = []
        path_losses = []
        los_probs = []
        
        point1 = np.array([self.radius * np.cos(np.radians(self.angle1)),
                          self.radius * np.sin(np.radians(self.angle1))])
        point2 = np.array([self.radius * np.cos(np.radians(self.angle2)),
                          self.radius * np.sin(np.radians(self.angle2))])
        
        for t in time_points:
            pos = point1 + t * (point2 - point1)
            distance_from_center = np.linalg.norm(pos)
            d_m = np.sqrt(distance_from_center**2 + self.uav_height**2)
            
            rates = self.calculate_uav_transmission_rates(
                self.uav_height, d_m, self.fc, self.Pm, self.Pu,
                self.Bgt, self.Bu, self.sigma_squared
            )
            
            uplink_rates.append(rates['uplink_rate']/1e6)
            downlink_rates.append(rates['downlink_rate']/1e6)
            path_losses.append(rates['path_loss_db'])
            los_probs.append(rates['los_probability'])
        
        # 绘制速率变化
        self.ax3.plot(time_points, uplink_rates, 'b-', label='上行速率', linewidth=2)
        self.ax3.plot(time_points, downlink_rates, 'r-', label='下行速率', linewidth=2)
        self.ax3.axvline(x=self.time_progress, color='orange', linestyle='--', linewidth=2, label='当前位置')
        
        self.ax3.set_xlabel('时间进度')
        self.ax3.set_ylabel('速率 (Mbps)')
        self.ax3.set_title('传输速率随时间变化')
        self.ax3.grid(True, alpha=0.3)
        self.ax3.legend()
        
    def plot_path_loss_analysis(self):
        # 绘制路径损耗和LoS概率
        time_points = np.linspace(0, 1, 50)
        path_losses = []
        los_probs = []
        
        point1 = np.array([self.radius * np.cos(np.radians(self.angle1)),
                          self.radius * np.sin(np.radians(self.angle1))])
        point2 = np.array([self.radius * np.cos(np.radians(self.angle2)),
                          self.radius * np.sin(np.radians(self.angle2))])
        
        for t in time_points:
            pos = point1 + t * (point2 - point1)
            distance_from_center = np.linalg.norm(pos)
            d_m = np.sqrt(distance_from_center**2 + self.uav_height**2)
            
            rates = self.calculate_uav_transmission_rates(
                self.uav_height, d_m, self.fc, self.Pm, self.Pu,
                self.Bgt, self.Bu, self.sigma_squared
            )
            
            path_losses.append(rates['path_loss_db'])
            los_probs.append(rates['los_probability'])
        
        # 双y轴图
        ax4_twin = self.ax4.twinx()
        
        line1 = self.ax4.plot(time_points, path_losses, 'g-', linewidth=2, label='路径损耗')
        line2 = ax4_twin.plot(time_points, los_probs, 'm-', linewidth=2, label='LoS概率')
        
        self.ax4.axvline(x=self.time_progress, color='orange', linestyle='--', linewidth=2)
        
        self.ax4.set_xlabel('时间进度')
        self.ax4.set_ylabel('路径损耗 (dB)', color='g')
        ax4_twin.set_ylabel('LoS概率', color='m')
        self.ax4.set_title('路径损耗和LoS概率')
        self.ax4.grid(True, alpha=0.3)
        
        # 合并图例
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        self.ax4.legend(lines, labels, loc='upper left')
        
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    # 创建并运行可视化系统
    visualizer = UAVTransmissionVisualizer()
    
    print("="*60)
    print("无人机传输速率实时可视化系统 - Python版")
    print("="*60)
    print()
    print("功能说明:")
    print("1. 调节'圆形半径'可以改变圆形区域大小")
    print("2. 调节'点1角度'和'点2角度'可以改变两点位置")
    print("3. '无人机高度'影响传输特性")
    print("4. '时间进度'控制无人机在点1到点2之间的位置")
    print("5. '开始动画'按钮可以自动播放无人机飞行过程")
    print()
    print("显示内容:")
    print("- 左上: 无人机飞行路径图")
    print("- 右上: 实时传输速率柱状图")
    print("- 左下: 传输速率随时间变化趋势")
    print("- 右下: 路径损耗和LoS概率分析")
    print()
    print("启动系统...")
    
    visualizer.run()