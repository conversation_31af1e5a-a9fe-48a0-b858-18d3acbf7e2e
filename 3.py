"""
来源文献：Federated deep reinforcement learning based trajectory design for UAV-assisted networks with mobile ground devices

本文件实现了一个多无人机（drone）在带障碍物的网格环境中，基于深度强化学习（DDQN）进行路径规划与协同训练的完整流程。

主要功能和结构说明：
1. 超参数集中管理：
   - 通过 DroneTrainingConfig 类集中管理训练、网络结构、环境、奖励、可视化等所有超参数，便于实验调整。
2. 环境建模：
   - DroneEnvironment 类实现了多无人机在二维网格世界中的运动、障碍物检测、目标点设置、奖励计算、轨迹记录与导出等功能。
3. 智能体与网络：
   - DDQN 类为可配置的深度 Q 网络，支持多层结构和正则化。
   - DroneAgent 类实现了基于 DDQN 的智能体，包含经验回放、epsilon-greedy 策略、目标网络同步、损失跟踪等。
4. 可视化与数据导出：
   - 提供静态轨迹图、动态 GIF 动画、训练过程曲线等多种可视化方法，并支持详细轨迹数据导出。
5. 主训练循环：
   - main() 函数组织了训练流程，包括环境与智能体初始化、训练与评估、早停、可视化与数据保存等。

适用场景：
- 多智能体协同路径规划、强化学习算法实验、无人机仿真、轨迹可视化与分析。
- 支持灵活配置起点、目标点、障碍物、奖励结构等，便于科研与教学。
"""
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import random
from collections import deque
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.animation import FuncAnimation, PillowWriter
import os
import time
from datetime import datetime

# ============================================================================
# CENTRALIZED HYPERPARAMETER CONFIGURATION
# ============================================================================

class DroneTrainingConfig:
    """
    Centralized configuration class for all uav training hyperparameters.
    Modify these values to experiment with different training settings.
    """

    # ========================================================================
    # TRAINING PARAMETERS
    # ========================================================================

    # Episode and training control
    MAX_EPISODES = 300                     # Maximum number of training episodes (reduced for demo)
    MAX_STEPS_PER_EPISODE = 100           # Maximum steps allowed per episode (reduced for demo)
    BATCH_SIZE = 32                      # Batch size for neural network training (reduced for demo)
    TARGET_UPDATE_FREQUENCY = 5         # How often to update target network (episodes)

    # Learning parameters
    LEARNING_RATE = 0.003                # Adam optimizer learning rate
    GAMMA = 0.95                         # Discount factor for future rewards
    MEMORY_BUFFER_SIZE = 20000           # Size of experience replay buffer
    GRADIENT_CLIP_THRESHOLD = 1.0        # Maximum gradient norm for clipping

    # Epsilon-greedy exploration parameters
    EPSILON_START = 0.9                  # Initial exploration rate
    EPSILON_MIN = 0.05                   # Minimum exploration rate
    EPSILON_DECAY = 0.995                # Decay rate for epsilon per episode

    # ========================================================================
    # NETWORK ARCHITECTURE PARAMETERS
    # ========================================================================

    # Neural network structure
    HIDDEN_LAYER_1_SIZE = 256            # First hidden layer size
    HIDDEN_LAYER_2_SIZE = 256            # Second hidden layer size
    HIDDEN_LAYER_3_SIZE = 128            # Third hidden layer size
    DROPOUT_RATE = 0.1                   # Dropout rate for regularization
    WEIGHT_DECAY = 1e-5                  # L2 regularization weight decay

    # ========================================================================
    # ENVIRONMENT PARAMETERS
    # ========================================================================

    # Grid world setup
    GRID_SIZE = 10                       # Size of the square grid (NxN)
    NUM_DRONES = 4                       # Number of drones in the environment
    OBSTACLE_POSITIONS = [(3, 3), (4, 4), (5, 5)]  # Fixed obstacle positions
    # >>> 新增起点和目标点超参数 <<<
    START_POSITIONS = [(0,0), (1,0), (2,0), (3,0)]               # 例: [(0,0), (1,0), (2,0), (3,0)]，None为默认
    TARGET_POSITIONS = [(6,9), (7,9), (8,9), (9,9)]              # 例: [(0,9), (1,9), (2,9), (3,9)]，None为默认

    # Training convergence criteria
    CONVERGENCE_THRESHOLD = 8.0          # Average reward threshold for convergence
    REQUIRED_CONSECUTIVE_EPISODES = 10   # Consecutive episodes above threshold needed

    # ========================================================================
    # VISUALIZATION PARAMETERS
    # ========================================================================

    # Animation settings
    ANIMATION_FPS = 3                    # Frames per second for GIF animations

    # Output generation intervals
    PLOT_GENERATION_INTERVAL = 10        # Generate plots every N episodes (reduced for demo)
    PLOT_GENERATION_EARLY_EPISODES = 2   # Always generate plots for first N episodes
    ANIMATION_GENERATION_INTERVAL = 15   # Generate animations every N episodes (reduced for demo)
    ANIMATION_GENERATION_EARLY_EPISODES = 3  # Always generate animations for first N episodes

    # Directory paths
    TRAJECTORY_PLOTS_DIR = "trajectory_plots"    # Directory for plots and animations
    TRAJECTORY_DATA_DIR = "trajectory_data"      # Directory for trajectory data files

    # Progress reporting
    PROGRESS_REPORT_INTERVAL = 10        # Print progress every N episodes
    PROGRESS_REPORT_EARLY_EPISODES = 5   # Always print progress for first N episodes

    # ========================================================================
    # REWARD STRUCTURE
    # ========================================================================

    # Reward values (defined here for easy modification)
    STEP_PENALTY = -0.1                  # Small penalty for each step taken
    COLLISION_PENALTY = -10.0            # Penalty for collisions with obstacles/drones
    TARGET_REWARD = 10.0                 # Reward for reaching target position

    @classmethod
    def print_configuration(cls):
        """Print current configuration settings for verification."""
        print("=" * 80)
        print("UAV TRAINING CONFIGURATION")
        print("=" * 80)
        print(f"Training Parameters:")
        print(f"  - Episodes: {cls.MAX_EPISODES}")
        print(f"  - Max Steps per Episode: {cls.MAX_STEPS_PER_EPISODE}")
        print(f"  - Batch Size: {cls.BATCH_SIZE}")
        print(f"  - Learning Rate: {cls.LEARNING_RATE}")
        print(f"  - Target Update Frequency: {cls.TARGET_UPDATE_FREQUENCY}")
        print(f"  - Epsilon: {cls.EPSILON_START} → {cls.EPSILON_MIN} (decay: {cls.EPSILON_DECAY})")
        print(f"  - Gamma: {cls.GAMMA}")
        print(f"  - Memory Buffer: {cls.MEMORY_BUFFER_SIZE}")
        print()
        print(f"Network Architecture:")
        print(f"  - Hidden Layers: {cls.HIDDEN_LAYER_1_SIZE}-{cls.HIDDEN_LAYER_2_SIZE}-{cls.HIDDEN_LAYER_3_SIZE}")
        print(f"  - Dropout Rate: {cls.DROPOUT_RATE}")
        print(f"  - Weight Decay: {cls.WEIGHT_DECAY}")
        print()
        print(f"Environment:")
        print(f"  - Grid Size: {cls.GRID_SIZE}x{cls.GRID_SIZE}")
        print(f"  - Number of Drones: {cls.NUM_DRONES}")
        print(f"  - Obstacles: {cls.OBSTACLE_POSITIONS}")
        print(f"  - Convergence Threshold: {cls.CONVERGENCE_THRESHOLD}")
        print()
        print(f"Visualization:")
        print(f"  - Animation FPS: {cls.ANIMATION_FPS}")
        print(f"  - Plot Interval: {cls.PLOT_GENERATION_INTERVAL}")
        print(f"  - Animation Interval: {cls.ANIMATION_GENERATION_INTERVAL}")
        print("=" * 80)

# Create a global config instance for easy access
CONFIG = DroneTrainingConfig()

# Grid Environment
class DroneEnvironment:
    def __init__(self, config=CONFIG):
        """Initialize uav environment with centralized configuration."""
        self.config = config
        self.grid_size = config.GRID_SIZE
        self.num_drones = config.NUM_DRONES
        self.obstacles = set(config.OBSTACLE_POSITIONS)  # Obstacle positions as (x, y) tuples
        self.drones = []  # List of (x, y) positions for drones
        self.targets = []  # List of (x, y) target positions for drones
        self.trajectories = []  # Track uav trajectories for visualization
        self.episode_trajectories = []  # Store trajectories for each episode
        # Enhanced trajectory tracking for data export
        self.detailed_trajectories = []  # Store detailed trajectory data for export
        self.current_episode_data = []  # Current episode detailed data
        self.episode_start_time = None
        self.step_count = 0
        self.reset()

    def reset(self):
        """Reset the environment with random uav and target positions."""

        # >>> 优先使用配置的起点和目标点 <<<
        if self.config.START_POSITIONS is not None:
            self.drones = [tuple(pos) for pos in self.config.START_POSITIONS]
        else:
            self.drones = [(i, 0) for i in range(self.num_drones)]  # Start at y=0

        if self.config.TARGET_POSITIONS is not None:
            self.targets = [tuple(pos) for pos in self.config.TARGET_POSITIONS]
        else:
            self.targets = [(i, self.grid_size-1) for i in range(self.num_drones)]  # Targets at top


        # self.drones = [(i, 0) for i in range(self.num_drones)]  # Start at y=0
        # self.targets = [(i, self.grid_size-1) for i in range(self.num_drones)]  # Targets at top
        # Initialize trajectory tracking for new episode
        self.trajectories = [[] for _ in range(self.num_drones)]
        for i in range(self.num_drones):
            self.trajectories[i].append(self.drones[i])

        # Initialize detailed trajectory tracking
        self.current_episode_data = []
        self.episode_start_time = datetime.now()
        self.step_count = 0

        # Record initial positions
        step_data = {
            'step': self.step_count,
            'timestamp': datetime.now(),
            'drone_positions': self.drones.copy(),
            'actions': [None] * self.num_drones,  # No action for initial state
            'rewards': [0.0] * self.num_drones,
            'done': False
        }
        self.current_episode_data.append(step_data)

        return [self.get_state(i) for i in range(self.num_drones)]

    def step(self, actions):
        """Execute actions for all drones and return states, rewards, done."""
        self.step_count += 1
        rewards = []
        next_states = []
        done = True
        new_positions = []

        # Calculate new positions
        for i, (action, (x, y)) in enumerate(zip(actions, self.drones)):
            if action == 0:  # Up
                new_x, new_y = x, min(y + 1, self.grid_size - 1)
            elif action == 1:  # Down
                new_x, new_y = x, max(y - 1, 0)
            elif action == 2:  # Left
                new_x, new_y = max(x - 1, 0), y
            else:  # Right
                new_x, new_y = min(x + 1, self.grid_size - 1), y
            new_positions.append((new_x, new_y))

        # Check collisions and assign rewards
        occupied = set(new_positions)
        for i, new_pos in enumerate(new_positions):
            reward = self.config.STEP_PENALTY  # Small penalty per step
            if new_pos in self.obstacles or (new_pos in occupied and new_positions.count(new_pos) > 1):
                reward = self.config.COLLISION_PENALTY  # Collision penalty
            elif new_pos == self.targets[i]:
                reward = self.config.TARGET_REWARD  # Target reached reward
            else:
                done = False  # Not all drones have reached targets
            rewards.append(reward)
            # Update uav position and trajectory
            if reward != self.config.COLLISION_PENALTY:  # Don't move if collision
                self.drones[i] = new_pos
                self.trajectories[i].append(new_pos)
            else:
                self.trajectories[i].append(self.drones[i])  # Stay at current position

        # Record detailed step data for export
        step_data = {
            'step': self.step_count,
            'timestamp': datetime.now(),
            'drone_positions': self.drones.copy(),
            'actions': actions.copy(),
            'rewards': rewards.copy(),
            'done': done
        }
        self.current_episode_data.append(step_data)

        next_states = [self.get_state(i) for i in range(self.num_drones)]
        return next_states, rewards, done

    def get_state(self, drone_id):
        """Return state vector for a uav."""
        x, y = self.drones[drone_id]
        tx, ty = self.targets[drone_id]
        state = [x, y, tx, ty]
        for obs in self.obstacles:
            state.extend(obs)
        for i, (dx, dy) in enumerate(self.drones):
            if i != drone_id:
                state.extend([dx, dy])
        return np.array(state)

    def save_episode_trajectory(self):
        """Save current episode trajectory for visualization and detailed data."""
        self.episode_trajectories.append([traj.copy() for traj in self.trajectories])
        self.detailed_trajectories.append(self.current_episode_data.copy())

    def export_trajectory_data(self, episode, save_path=None):
        """Export detailed trajectory data to text file."""
        if save_path is None:
            save_path = self.config.TRAJECTORY_DATA_DIR
        if not os.path.exists(save_path):
            os.makedirs(save_path)

        if episode >= len(self.detailed_trajectories):
            return

        episode_data = self.detailed_trajectories[episode]
        filename = f"{save_path}/drone_trajectories_episode_{episode}.txt"

        with open(filename, 'w') as f:
            # Write header information
            f.write(f"UAV Trajectory Data - Episode {episode}\n")
            f.write(f"Episode Start Time: {episode_data[0]['timestamp'].strftime('%Y-%m-%d %H:%M:%S.%f')}\n")
            f.write(f"Grid Size: {self.grid_size}x{self.grid_size}\n")
            f.write(f"Number of Drones: {self.num_drones}\n")
            f.write(f"Obstacles: {list(self.obstacles)}\n")
            f.write(f"Targets: {self.targets}\n")
            f.write(f"Total Steps: {len(episode_data) - 1}\n")  # -1 because first entry is initial state
            f.write("=" * 80 + "\n\n")

            # Write step-by-step data
            f.write("Step\tTimestamp\t\t\t")
            for i in range(self.num_drones):
                f.write(f"UAV{i}_X\tDrone{i}_Y\tAction{i}\tReward{i}\t")
            f.write("Episode_Done\n")
            f.write("-" * 120 + "\n")

            action_names = ['UP', 'DOWN', 'LEFT', 'RIGHT']

            for step_data in episode_data:
                step = step_data['step']
                timestamp = step_data['timestamp'].strftime('%H:%M:%S.%f')[:-3]  # Remove last 3 digits of microseconds
                positions = step_data['drone_positions']
                actions = step_data['actions']
                rewards = step_data['rewards']
                done = step_data['done']

                f.write(f"{step}\t{timestamp}\t\t")
                for i in range(self.num_drones):
                    x, y = positions[i]
                    action_str = action_names[actions[i]] if actions[i] is not None else 'INIT'
                    reward = rewards[i]
                    f.write(f"{x}\t{y}\t{action_str}\t{reward:.1f}\t")
                f.write(f"{done}\n")

            # Write summary statistics
            f.write("\n" + "=" * 80 + "\n")
            f.write("EPISODE SUMMARY\n")
            f.write("=" * 80 + "\n")

            total_rewards = [sum(step_data['rewards'][i] for step_data in episode_data) for i in range(self.num_drones)]
            f.write(f"Total Rewards per UAV: {total_rewards}\n")
            f.write(f"Average Reward per UAV: {[r/(len(episode_data)-1) for r in total_rewards]}\n")
            f.write(f"Episode Duration: {len(episode_data) - 1} steps\n")

            # Final positions
            final_positions = episode_data[-1]['drone_positions']
            f.write(f"Final Positions: {final_positions}\n")
            f.write(f"Target Positions: {self.targets}\n")

            # Check which drones reached targets
            targets_reached = [pos == target for pos, target in zip(final_positions, self.targets)]
            f.write(f"Targets Reached: {targets_reached}\n")
            f.write(f"Success Rate: {sum(targets_reached)}/{self.num_drones} ({100*sum(targets_reached)/self.num_drones:.1f}%)\n")

# Optimized DDQN Network with configurable architecture
class DDQN(nn.Module):
    def __init__(self, state_dim, action_dim, config=CONFIG):
        super(DDQN, self).__init__()
        # Configurable network architecture
        self.fc1 = nn.Linear(state_dim, config.HIDDEN_LAYER_1_SIZE)
        self.fc2 = nn.Linear(config.HIDDEN_LAYER_1_SIZE, config.HIDDEN_LAYER_2_SIZE)
        self.fc3 = nn.Linear(config.HIDDEN_LAYER_2_SIZE, config.HIDDEN_LAYER_3_SIZE)
        self.fc4 = nn.Linear(config.HIDDEN_LAYER_3_SIZE, action_dim)
        self.dropout = nn.Dropout(config.DROPOUT_RATE)  # Configurable dropout for regularization

    def forward(self, x):
        x = torch.relu(self.fc1(x))
        x = self.dropout(x)
        x = torch.relu(self.fc2(x))
        x = self.dropout(x)
        x = torch.relu(self.fc3(x))
        return self.fc4(x)

# Optimized UAV Agent with configurable hyperparameters
class DroneAgent:
    def __init__(self, state_dim, action_dim, config=CONFIG):
        self.config = config
        self.state_dim = state_dim
        self.action_dim = action_dim
        # Configurable epsilon parameters
        self.epsilon = config.EPSILON_START  # Start with configured exploration rate
        self.epsilon_min = config.EPSILON_MIN  # Minimum exploration rate
        self.epsilon_decay = config.EPSILON_DECAY  # Decay rate for epsilon
        self.gamma = config.GAMMA  # Discount factor
        self.main_net = DDQN(state_dim, action_dim, config)
        self.target_net = DDQN(state_dim, action_dim, config)
        self.target_net.load_state_dict(self.main_net.state_dict())
        # Configurable optimizer settings
        self.optimizer = optim.Adam(self.main_net.parameters(),
                                  lr=config.LEARNING_RATE,
                                  weight_decay=config.WEIGHT_DECAY)
        self.memory = deque(maxlen=config.MEMORY_BUFFER_SIZE)  # Configurable memory size
        self.loss_history = []  # Track training loss

    def select_action(self, state):
        """Choose an action using epsilon-greedy policy with decay."""
        if random.random() < self.epsilon:
            return random.randint(0, self.action_dim - 1)
        state = torch.FloatTensor(state).unsqueeze(0)
        with torch.no_grad():
            q_values = self.main_net(state)
        return q_values.argmax().item()

    def decay_epsilon(self):
        """Decay epsilon for reduced exploration over time."""
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay

    def store_experience(self, state, action, reward, next_state, done):
        """Store experience in replay memory."""
        self.memory.append((state, action, reward, next_state, done))

    def train(self, batch_size):
        """Train the main network using a batch from memory with improved loss tracking."""
        if len(self.memory) < batch_size:
            return None
        batch = random.sample(self.memory, batch_size)
        states, actions, rewards, next_states, dones = zip(*batch)

        states = torch.FloatTensor(states)
        actions = torch.LongTensor(actions)
        rewards = torch.FloatTensor(rewards)
        next_states = torch.FloatTensor(next_states)
        dones = torch.FloatTensor(dones)

        q_values = self.main_net(states).gather(1, actions.unsqueeze(1)).squeeze(1)
        next_actions = self.main_net(next_states).argmax(1)
        next_q_values = self.target_net(next_states).gather(1, next_actions.unsqueeze(1)).squeeze(1)
        targets = rewards + (1 - dones) * self.gamma * next_q_values

        loss = nn.MSELoss()(q_values, targets.detach())
        self.optimizer.zero_grad()
        loss.backward()
        # Configurable gradient clipping for stability
        torch.nn.utils.clip_grad_norm_(self.main_net.parameters(),
                                     max_norm=self.config.GRADIENT_CLIP_THRESHOLD)
        self.optimizer.step()

        # Track loss for monitoring
        self.loss_history.append(loss.item())
        return loss.item()

    def update_target(self):
        """Update target network with main network weights."""
        self.target_net.load_state_dict(self.main_net.state_dict())

# Visualization Functions
def create_animated_trajectory(env, episode, save_path=None, fps=None):
    """Create animated GIF showing uav movement during an episode."""
    if save_path is None:
        save_path = CONFIG.TRAJECTORY_PLOTS_DIR
    if fps is None:
        fps = CONFIG.ANIMATION_FPS
    if not os.path.exists(save_path):
        os.makedirs(save_path)

    if episode >= len(env.detailed_trajectories):
        print(f"Episode {episode} data not available for animation")
        return

    episode_data = env.detailed_trajectories[episode]
    if len(episode_data) < 2:
        print(f"Episode {episode} has insufficient data for animation")
        return

    # Set up the figure and axis
    fig, ax = plt.subplots(figsize=(10, 10))
    ax.set_xlim(-0.5, env.grid_size - 0.5)
    ax.set_ylim(-0.5, env.grid_size - 0.5)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.set_title(f'UAV Animation - Episode {episode}', fontsize=14, fontweight='bold')
    ax.set_xlabel('X Position', fontsize=12)
    ax.set_ylabel('Y Position', fontsize=12)

    # Draw static elements (obstacles and targets)
    for obs in env.obstacles:
        rect = patches.Rectangle((obs[0]-0.4, obs[1]-0.4), 0.8, 0.8,
                               linewidth=2, edgecolor='red', facecolor='red', alpha=0.7)
        ax.add_patch(rect)

    for i, target in enumerate(env.targets):
        circle = patches.Circle(target, 0.3, linewidth=2, edgecolor='green',
                              facecolor='lightgreen', alpha=0.7)
        ax.add_patch(circle)
        ax.text(target[0], target[1], f'T{i}', ha='center', va='center', fontweight='bold')

    # Colors for different drones
    colors = ['blue', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']

    # Initialize uav markers and trail lines
    drone_markers = []
    trail_lines = []
    step_text = ax.text(0.02, 0.98, '', transform=ax.transAxes, fontsize=12,
                       verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    for i in range(env.num_drones):
        # UAV marker
        marker = ax.plot([], [], 'o', color=colors[i % len(colors)], markersize=12,
                        markeredgecolor='black', markeredgewidth=2, label=f'UAV {i}')[0]
        drone_markers.append(marker)

        # Trail line
        trail = ax.plot([], [], '-', color=colors[i % len(colors)], linewidth=2, alpha=0.6)[0]
        trail_lines.append(trail)

    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    def animate(frame):
        """Animation function for each frame."""
        if frame >= len(episode_data):
            return drone_markers + trail_lines + [step_text]

        step_data = episode_data[frame]
        positions = step_data['drone_positions']
        actions = step_data['actions']
        rewards = step_data['rewards']

        # Update step information text
        action_names = ['UP', 'DOWN', 'LEFT', 'RIGHT']
        info_text = f"Step: {step_data['step']}\n"
        for i in range(env.num_drones):
            action_str = action_names[actions[i]] if actions[i] is not None else 'INIT'
            info_text += f"D{i}: ({positions[i][0]},{positions[i][1]}) {action_str} R:{rewards[i]:.1f}\n"
        step_text.set_text(info_text)

        # Update uav positions and trails
        for i in range(env.num_drones):
            # Update uav marker position
            x, y = positions[i]
            drone_markers[i].set_data([x], [y])

            # Update trail (show path up to current frame)
            trail_x = [episode_data[j]['drone_positions'][i][0] for j in range(frame + 1)]
            trail_y = [episode_data[j]['drone_positions'][i][1] for j in range(frame + 1)]
            trail_lines[i].set_data(trail_x, trail_y)

        return drone_markers + trail_lines + [step_text]

    # Create animation
    anim = FuncAnimation(fig, animate, frames=len(episode_data), interval=1000//fps,
                        blit=True, repeat=True)

    # Save as GIF
    gif_filename = f"{save_path}/drone_animation_episode_{episode}.gif"
    print(f"Creating animation for episode {episode}... (this may take a moment)")

    try:
        writer = PillowWriter(fps=fps)
        anim.save(gif_filename, writer=writer)
        print(f"Animation saved: {gif_filename}")
    except Exception as e:
        print(f"Error saving animation: {e}")
        # Fallback: save as MP4 if available
        try:
            anim.save(f"{save_path}/drone_animation_episode_{episode}.mp4", fps=fps)
            print(f"Animation saved as MP4: {save_path}/drone_animation_episode_{episode}.mp4")
        except:
            print("Could not save animation in any format")

    plt.close(fig)

def plot_trajectory(env, episode, save_path=None):
    """Plot uav trajectories for a specific episode."""
    if save_path is None:
        save_path = CONFIG.TRAJECTORY_PLOTS_DIR
    if not os.path.exists(save_path):
        os.makedirs(save_path)

    fig, ax = plt.subplots(figsize=(10, 10))

    # Draw grid
    ax.set_xlim(-0.5, env.grid_size - 0.5)
    ax.set_ylim(-0.5, env.grid_size - 0.5)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)

    # Draw obstacles
    for obs in env.obstacles:
        rect = patches.Rectangle((obs[0]-0.4, obs[1]-0.4), 0.8, 0.8,
                               linewidth=2, edgecolor='red', facecolor='red', alpha=0.7)
        ax.add_patch(rect)

    # Draw targets
    for i, target in enumerate(env.targets):
        circle = patches.Circle(target, 0.3, linewidth=2, edgecolor='green',
                              facecolor='lightgreen', alpha=0.7)
        ax.add_patch(circle)
        ax.text(target[0], target[1], f'T{i}', ha='center', va='center', fontweight='bold')

    # Draw trajectories
    colors = ['blue', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
    if episode < len(env.episode_trajectories):
        trajectories = env.episode_trajectories[episode]
        for i, traj in enumerate(trajectories):
            if len(traj) > 1:
                x_coords = [pos[0] for pos in traj]
                y_coords = [pos[1] for pos in traj]
                ax.plot(x_coords, y_coords, color=colors[i % len(colors)],
                       linewidth=2, marker='o', markersize=4, alpha=0.8, label=f'UAV {i}')
                # Mark start and end positions
                ax.plot(x_coords[0], y_coords[0], color=colors[i % len(colors)],
                       marker='s', markersize=8, label=f'Start {i}')
                ax.plot(x_coords[-1], y_coords[-1], color=colors[i % len(colors)],
                       marker='^', markersize=8, label=f'End {i}')

    ax.set_title(f'UAV Trajectories - Episode {episode}', fontsize=14, fontweight='bold')
    ax.set_xlabel('X Position', fontsize=12)
    ax.set_ylabel('Y Position', fontsize=12)
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    plt.tight_layout()
    plt.savefig(f'{save_path}/episode_{episode}_trajectory.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_training_progress(episode_rewards, episode_losses, save_path=None):
    """Plot training progress including rewards and losses."""
    if save_path is None:
        save_path = CONFIG.TRAJECTORY_PLOTS_DIR
    if not os.path.exists(save_path):
        os.makedirs(save_path)

    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

    # Plot episode rewards
    episodes = range(len(episode_rewards))
    avg_rewards = [np.mean(rewards) for rewards in episode_rewards]

    ax1.plot(episodes, avg_rewards, 'b-', linewidth=2, label='Average Reward')
    ax1.set_title('Training Progress - Average Reward per Episode', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Episode', fontsize=12)
    ax1.set_ylabel('Average Reward', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.legend()

    # Plot training losses
    if episode_losses:
        ax2.plot(range(len(episode_losses)), episode_losses, 'r-', linewidth=1, alpha=0.7)
        # Add moving average for smoother visualization
        if len(episode_losses) > 50:
            window_size = min(50, len(episode_losses) // 10)
            moving_avg = np.convolve(episode_losses, np.ones(window_size)/window_size, mode='valid')
            ax2.plot(range(window_size-1, len(episode_losses)), moving_avg, 'darkred', linewidth=2, label='Moving Average')

        ax2.set_title('Training Loss Over Time', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Training Step', fontsize=12)
        ax2.set_ylabel('Loss', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.legend()

    plt.tight_layout()
    plt.savefig(f'{save_path}/training_progress.png', dpi=300, bbox_inches='tight')
    plt.close()

# Optimized Main Training Loop with Centralized Configuration
def main():
    print("Starting Optimized UAV Training with Centralized Configuration...")

    # Print configuration for verification
    CONFIG.print_configuration()

    # Environment setup using centralized config
    env = DroneEnvironment(CONFIG)
    state_dim = 4 + 2 * len(CONFIG.OBSTACLE_POSITIONS) + 2 * (CONFIG.NUM_DRONES - 1)  # [x, y, tx, ty, obstacles, other drones]
    action_dim = 4  # Up, Down, Left, Right

    # Initialize agents with centralized configuration
    agents = [DroneAgent(state_dim, action_dim, CONFIG) for _ in range(CONFIG.NUM_DRONES)]

    # Training tracking
    episode_rewards = []
    all_losses = []
    consecutive_good_episodes = 0

    start_time = time.time()

    for episode in range(CONFIG.MAX_EPISODES):
        states = env.reset()
        done = False
        total_rewards = [0] * CONFIG.NUM_DRONES
        episode_losses = []
        step = 0

        while not done and step < CONFIG.MAX_STEPS_PER_EPISODE:
            actions = [agent.select_action(states[i]) for i, agent in enumerate(agents)]
            next_states, rewards, done = env.step(actions)

            # Store experiences and train
            for i, agent in enumerate(agents):
                agent.store_experience(states[i], actions[i], rewards[i], next_states[i], done)
                loss = agent.train(CONFIG.BATCH_SIZE)
                if loss is not None:
                    episode_losses.append(loss)
                total_rewards[i] += rewards[i]

            states = next_states
            step += 1

            # Update target networks
            if step % CONFIG.TARGET_UPDATE_FREQUENCY == 0:
                for agent in agents:
                    agent.update_target()

        # Decay epsilon for all agents
        for agent in agents:
            agent.decay_epsilon()

        # Save episode trajectory
        env.save_episode_trajectory()

        # Track training progress
        episode_rewards.append(total_rewards)
        if episode_losses:
            all_losses.extend(episode_losses)

        avg_reward = np.mean(total_rewards)

        # Check for convergence
        if avg_reward >= CONFIG.CONVERGENCE_THRESHOLD:
            consecutive_good_episodes += 1
        else:
            consecutive_good_episodes = 0

        # Progress reporting
        if episode % CONFIG.PROGRESS_REPORT_INTERVAL == 0 or episode < CONFIG.PROGRESS_REPORT_EARLY_EPISODES:
            elapsed_time = time.time() - start_time
            print(f"Episode {episode:3d} | Avg Reward: {avg_reward:6.2f} | "
                  f"Steps: {step:3d} | Epsilon: {agents[0].epsilon:.3f} | "
                  f"Time: {elapsed_time:.1f}s")

        # Generate trajectory plots and export data for selected episodes
        if episode % CONFIG.PLOT_GENERATION_INTERVAL == 0 or episode < CONFIG.PLOT_GENERATION_EARLY_EPISODES:
            plot_trajectory(env, episode)
            # Export trajectory data to text file
            env.export_trajectory_data(episode)
            # Create animated GIF (for first few episodes and every interval)
            if episode < CONFIG.ANIMATION_GENERATION_EARLY_EPISODES or episode % CONFIG.ANIMATION_GENERATION_INTERVAL == 0:
                create_animated_trajectory(env, episode)

        # Early stopping if converged
        if consecutive_good_episodes >= CONFIG.REQUIRED_CONSECUTIVE_EPISODES:
            print(f"\nConverged after {episode + 1} episodes!")
            print(f"Average reward maintained above {CONFIG.CONVERGENCE_THRESHOLD} for {CONFIG.REQUIRED_CONSECUTIVE_EPISODES} consecutive episodes.")
            break

    total_time = time.time() - start_time
    print(f"\nTraining completed in {total_time:.2f} seconds")
    print(f"Final average reward: {np.mean(episode_rewards[-1]):.2f}")
    print(f"Final epsilon: {agents[0].epsilon:.3f}")

    # Generate final visualizations and data exports
    print("\nGenerating training progress plots...")
    plot_training_progress(episode_rewards, all_losses)

    # Plot final episode trajectory and export data
    final_episode = len(env.episode_trajectories) - 1
    if final_episode >= 0:
        print(f"\nGenerating final episode ({final_episode}) visualizations...")
        plot_trajectory(env, final_episode)
        env.export_trajectory_data(final_episode)
        create_animated_trajectory(env, final_episode)

        print(f"Trajectory plots saved in '{CONFIG.TRAJECTORY_PLOTS_DIR}' directory")
        print(f"Trajectory data exported to '{CONFIG.TRAJECTORY_DATA_DIR}' directory")

        # Export data for a few key episodes
        key_episodes = [0, len(env.episode_trajectories) // 4, len(env.episode_trajectories) // 2,
                       3 * len(env.episode_trajectories) // 4, final_episode]
        key_episodes = list(set([ep for ep in key_episodes if 0 <= ep < len(env.episode_trajectories)]))

        print(f"\nExporting trajectory data for key episodes: {key_episodes}")
        for ep in key_episodes:
            if ep != final_episode:  # Already exported above
                env.export_trajectory_data(ep)

    print("\nTraining, visualization, and data export complete!")
    print(f"Check the following directories:")
    print(f"  - '{CONFIG.TRAJECTORY_PLOTS_DIR}/': Static plots and GIF animations")
    print(f"  - '{CONFIG.TRAJECTORY_DATA_DIR}/': Detailed trajectory data in text format")
    print(f"\nConfiguration used:")
    print(f"  - Episodes: {CONFIG.MAX_EPISODES}")
    print(f"  - Learning Rate: {CONFIG.LEARNING_RATE}")
    print(f"  - Network: {CONFIG.HIDDEN_LAYER_1_SIZE}-{CONFIG.HIDDEN_LAYER_2_SIZE}-{CONFIG.HIDDEN_LAYER_3_SIZE}")
    print(f"  - Batch Size: {CONFIG.BATCH_SIZE}")
    print(f"  - Epsilon: {CONFIG.EPSILON_START} → {CONFIG.EPSILON_MIN}")
    print("=" * 80)

if __name__ == "__main__":
    main()