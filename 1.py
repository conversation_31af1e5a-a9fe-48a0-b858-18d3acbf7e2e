"""
本代码实现了基于 Gym 环境（CartPole-v1）的深度强化学习智能体训练与性能对比。

主要内容：
1. 定义了普通 DDQN（Double DQN）和改进的 Dueling DDQN（决斗网络结构）两种神经网络结构。
2. 采用经验回放、epsilon-greedy 策略和多步回报等机制进行训练。
3. 训练过程中分别记录奖励、损失等指标，并定期同步目标网络。
4. 训练结束后自动生成奖励、损失、统计对比等多种可视化图表，便于分析两种算法的性能差异。
5. 代码结构清晰，适合用于深度强化学习算法的实验、对比与性能评估。
"""

import torch
import torch.nn as nn
import torch.optim as optim
import gym
import random
import numpy as np
from collections import deque
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.gridspec import GridSpec
import seaborn as sns

# 设置随机种子以确保可复现性
torch.manual_seed(0)
np.random.seed(0)
random.seed(0)

# 超参数
GAMMA = 0.99           # 折扣因子
LEARNING_RATE = 0.001  # 学习率
BATCH_SIZE = 32        # 批量大小
MEMORY_SIZE = 10000    # 经验回放缓冲区容量
TARGET_UPDATE = 10    # 目标网络更新频率
N_STEPS = 60            # 多步自举的步长
EPISODES = 1000         # 训练回合数
EPSILON_START = 1.0    # 初始探索率
EPSILON_END = 0.01     # 最终探索率
EPSILON_DECAY = 0.995  # 探索率衰减

# 创建环境
env = gym.make('CartPole-v1')
state_dim = env.observation_space.shape[0]
action_dim = env.action_space.n

# 普通DDQN网络
class DDQN(nn.Module):
    def __init__(self, state_dim, action_dim):
        super(DDQN, self).__init__()
        self.fc1 = nn.Linear(state_dim, 128)
        self.fc2 = nn.Linear(128, 128)
        self.fc3 = nn.Linear(128, action_dim)

    def forward(self, x):
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        return self.fc3(x)

# 改进DDQN网络（Dueling Network）
class DuelingDDQN(nn.Module):
    def __init__(self, state_dim, action_dim):
        super(DuelingDDQN, self).__init__()
        self.feature = nn.Sequential(
            nn.Linear(state_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 128),
            nn.ReLU()
        )
        self.value_stream = nn.Linear(128, 1)          # 状态价值流
        self.advantage_stream = nn.Linear(128, action_dim)  # 动作优势流

    def forward(self, x):
        features = self.feature(x)
        value = self.value_stream(features)
        advantage = self.advantage_stream(features)
        # Q = V + (A - mean(A))
        q_values = value + (advantage - advantage.mean(dim=1, keepdim=True))
        return q_values

# 经验回放缓冲区
class ReplayBuffer:
    def __init__(self, capacity):
        self.buffer = deque(maxlen=capacity)

    def push(self, state, action, reward, next_state, done):
        self.buffer.append((state, action, reward, next_state, done))

    def sample(self, batch_size):
        return random.sample(self.buffer, batch_size)

    def __len__(self):
        return len(self.buffer)

# 计算多步回报（仅用于改进DDQN）
def calc_multi_step_return(rewards, gamma, n_steps):
    returns = []
    for i in range(len(rewards)):
        max_step = min(i + n_steps, len(rewards))
        ret = sum([gamma ** j * rewards[i + j] for j in range(max_step - i)])
        returns.append(ret)
    return returns

# Epsilon-greedy动作选择
def select_action(net, state, epsilon, action_dim):
    """
    使用epsilon-greedy策略选择动作
    """
    if random.random() < epsilon:
        return random.randint(0, action_dim - 1)  # 随机探索
    else:
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0)
            q_values = net(state_tensor)
            return q_values.argmax().item()  # 贪婪选择

# 训练函数（支持普通DDQN和改进DDQN）
def train_ddqn(net, target_net, optimizer, buffer, batch_size, gamma, n_steps=None):
    if len(buffer) < batch_size:
        return None

    batch = buffer.sample(batch_size)
    states, actions, rewards, next_states, dones = zip(*batch)

    # 优化张量转换以避免警告
    states = torch.FloatTensor(np.array(states))
    actions = torch.LongTensor(np.array(actions))
    rewards = torch.FloatTensor(np.array(rewards))
    next_states = torch.FloatTensor(np.array(next_states))
    dones = torch.FloatTensor(np.array(dones))

    if n_steps is None:  # 普通DDQN：单步TD更新
        q_values = net(states).gather(1, actions.unsqueeze(1)).squeeze(1)
        next_q_values = net(next_states).detach()
        next_actions = next_q_values.argmax(1)
        next_q_values = target_net(next_states).detach().gather(1, next_actions.unsqueeze(1)).squeeze(1)
        target = rewards + gamma * next_q_values * (1 - dones)
        loss = (q_values - target).pow(2).mean()
    else:  # 改进DDQN：多步自举
        # 注意：这里简化处理，假设采样的是连续n步的回报
        multi_step_rewards = calc_multi_step_return(rewards, gamma, n_steps)
        q_values = net(states).gather(1, actions.unsqueeze(1)).squeeze(1)
        next_q_values = net(next_states).detach()
        next_actions = next_q_values.argmax(1)
        next_q_values = target_net(next_states).detach().gather(1, next_actions.unsqueeze(1)).squeeze(1)
        target = torch.FloatTensor(multi_step_rewards) + (gamma ** n_steps) * next_q_values * (1 - dones)
        loss = (q_values - target).pow(2).mean()

    optimizer.zero_grad()
    loss.backward()
    optimizer.step()
    return loss.item()

# 创建比较可视化函数
def create_comparison_visualization(ddqn_rewards, dueling_rewards, ddqn_losses, dueling_losses):
    """
    创建DDQN和Dueling DDQN的比较可视化
    包括奖励对比、损失对比、差异分析等
    """
    try:
        plt.style.use('seaborn-v0_8')  # 使用更现代的样式
    except:
        plt.style.use('default')  # 如果seaborn样式不可用，使用默认样式
    fig = plt.figure(figsize=(16, 12))
    gs = GridSpec(3, 3, figure=fig, hspace=0.3, wspace=0.3)

    # 1. 奖励对比 - 时间序列
    ax1 = fig.add_subplot(gs[0, :2])
    episodes = range(len(ddqn_rewards))
    ax1.plot(episodes, ddqn_rewards, label='DDQN', color='blue', alpha=0.7, linewidth=1)
    ax1.plot(episodes, dueling_rewards, label='Dueling DDQN', color='red', alpha=0.7, linewidth=1)

    # 添加移动平均线
    window = 20
    if len(ddqn_rewards) >= window:
        ddqn_ma = np.convolve(ddqn_rewards, np.ones(window)/window, mode='valid')
        dueling_ma = np.convolve(dueling_rewards, np.ones(window)/window, mode='valid')
        ax1.plot(range(window-1, len(ddqn_rewards)), ddqn_ma,
                color='blue', linewidth=2, label='DDQN (MA)')
        ax1.plot(range(window-1, len(dueling_rewards)), dueling_ma,
                color='red', linewidth=2, label='Dueling DDQN (MA)')

    ax1.set_title('Reward Comparison Over Episodes', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Episode')
    ax1.set_ylabel('Reward')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. 奖励分布对比
    ax2 = fig.add_subplot(gs[0, 2])
    ax2.hist(ddqn_rewards, bins=30, alpha=0.6, label='DDQN', color='blue', density=True)
    ax2.hist(dueling_rewards, bins=30, alpha=0.6, label='Dueling DDQN', color='red', density=True)
    ax2.set_title('Reward Distribution', fontsize=12, fontweight='bold')
    ax2.set_xlabel('Reward')
    ax2.set_ylabel('Density')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 3. 损失对比
    if ddqn_losses and dueling_losses:
        ax3 = fig.add_subplot(gs[1, :2])
        loss_episodes = range(len(ddqn_losses))
        ax3.plot(loss_episodes, ddqn_losses, label='DDQN Loss', color='blue', alpha=0.7)
        ax3.plot(loss_episodes, dueling_losses, label='Dueling DDQN Loss', color='red', alpha=0.7)
        ax3.set_title('Training Loss Comparison', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Training Step')
        ax3.set_ylabel('Loss')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.set_yscale('log')  # 使用对数刻度

    # 4. 性能统计对比
    ax4 = fig.add_subplot(gs[1, 2])
    stats_data = {
        'Mean Reward': [np.mean(ddqn_rewards), np.mean(dueling_rewards)],
        'Max Reward': [np.max(ddqn_rewards), np.max(dueling_rewards)],
        'Std Reward': [np.std(ddqn_rewards), np.std(dueling_rewards)]
    }

    x = np.arange(len(stats_data))
    width = 0.35

    ddqn_stats = [stats_data[key][0] for key in stats_data.keys()]
    dueling_stats = [stats_data[key][1] for key in stats_data.keys()]

    ax4.bar(x - width/2, ddqn_stats, width, label='DDQN', color='blue', alpha=0.7)
    ax4.bar(x + width/2, dueling_stats, width, label='Dueling DDQN', color='red', alpha=0.7)

    ax4.set_title('Performance Statistics', fontsize=12, fontweight='bold')
    ax4.set_xticks(x)
    ax4.set_xticklabels(stats_data.keys(), rotation=45)
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    # 5. 奖励差异分析
    ax5 = fig.add_subplot(gs[2, :])
    reward_diff = np.array(dueling_rewards) - np.array(ddqn_rewards)
    ax5.plot(episodes, reward_diff, color='green', alpha=0.7, linewidth=1)
    ax5.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax5.fill_between(episodes, reward_diff, 0,
                    where=(reward_diff > 0), color='green', alpha=0.3, label='Dueling DDQN Better')
    ax5.fill_between(episodes, reward_diff, 0,
                    where=(reward_diff < 0), color='red', alpha=0.3, label='DDQN Better')

    ax5.set_title('Performance Difference (Dueling DDQN - DDQN)', fontsize=14, fontweight='bold')
    ax5.set_xlabel('Episode')
    ax5.set_ylabel('Reward Difference')
    ax5.legend()
    ax5.grid(True, alpha=0.3)

    # 添加总体标题和统计信息
    fig.suptitle('DDQN vs Dueling DDQN Performance Comparison', fontsize=16, fontweight='bold')

    # 添加文本统计信息
    mean_diff = np.mean(reward_diff)
    improvement_episodes = np.sum(reward_diff > 0)
    total_episodes = len(reward_diff)

    stats_text = f"""
    Performance Summary:
    • Average Reward Difference: {mean_diff:.2f}
    • Episodes where Dueling DDQN performed better: {improvement_episodes}/{total_episodes} ({100*improvement_episodes/total_episodes:.1f}%)
    • DDQN Mean Reward: {np.mean(ddqn_rewards):.2f} ± {np.std(ddqn_rewards):.2f}
    • Dueling DDQN Mean Reward: {np.mean(dueling_rewards):.2f} ± {np.std(dueling_rewards):.2f}
    """

    fig.text(0.02, 0.02, stats_text, fontsize=10, verticalalignment='bottom',
             bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))

    plt.tight_layout()
    plt.savefig('ddqn_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("\n" + "="*60)
    print("PERFORMANCE COMPARISON SUMMARY")
    print("="*60)
    print(f"DDQN - Mean: {np.mean(ddqn_rewards):.2f}, Std: {np.std(ddqn_rewards):.2f}, Max: {np.max(ddqn_rewards):.2f}")
    print(f"Dueling DDQN - Mean: {np.mean(dueling_rewards):.2f}, Std: {np.std(dueling_rewards):.2f}, Max: {np.max(dueling_rewards):.2f}")
    print(f"Average Improvement: {mean_diff:.2f}")
    print(f"Dueling DDQN performed better in {improvement_episodes}/{total_episodes} episodes ({100*improvement_episodes/total_episodes:.1f}%)")
    print("="*60)

# 主训练循环
def main():
    # 初始化普通DDQN
    ddqn_net = DDQN(state_dim, action_dim)
    ddqn_target_net = DDQN(state_dim, action_dim)
    ddqn_target_net.load_state_dict(ddqn_net.state_dict())
    ddqn_optimizer = optim.Adam(ddqn_net.parameters(), lr=LEARNING_RATE)
    ddqn_buffer = ReplayBuffer(MEMORY_SIZE)

    # 初始化改进DDQN
    dueling_net = DuelingDDQN(state_dim, action_dim)
    dueling_target_net = DuelingDDQN(state_dim, action_dim)
    dueling_target_net.load_state_dict(dueling_net.state_dict())
    dueling_optimizer = optim.Adam(dueling_net.parameters(), lr=LEARNING_RATE)
    dueling_buffer = ReplayBuffer(MEMORY_SIZE)

    # 用于存储训练结果的列表
    ddqn_rewards = []
    dueling_rewards = []
    ddqn_losses = []
    dueling_losses = []

    # 初始化epsilon
    epsilon = EPSILON_START

    print("开始训练...")
    print(f"总回合数: {EPISODES}")
    print("-" * 50)

    try:
        for episode in range(EPISODES):
            state, _ = env.reset()  # 新版gym返回(observation, info)
            done = False
            truncated = False
            ddqn_episode_reward = 0
            dueling_episode_reward = 0

            # 为了公平比较，两个网络使用相同的经验
            episode_transitions = []

            while not (done or truncated):
                # 使用epsilon-greedy策略选择动作（这里用DDQN网络选择）
                action = select_action(ddqn_net, state, epsilon, action_dim)
                next_state, reward, done, truncated, _ = env.step(action)  # 新版gym返回5个值

                # 存储转换
                episode_transitions.append((state, action, reward, next_state, done or truncated))
                state = next_state
                ddqn_episode_reward += reward
                dueling_episode_reward += reward

            # 将经验添加到两个缓冲区
            for transition in episode_transitions:
                ddqn_buffer.push(*transition)
                dueling_buffer.push(*transition)

            # 训练网络
            ddqn_loss = train_ddqn(ddqn_net, ddqn_target_net, ddqn_optimizer, ddqn_buffer, BATCH_SIZE, GAMMA)
            dueling_loss = train_ddqn(dueling_net, dueling_target_net, dueling_optimizer, dueling_buffer, BATCH_SIZE, GAMMA, N_STEPS)

            # 记录训练结果
            ddqn_rewards.append(ddqn_episode_reward)
            dueling_rewards.append(dueling_episode_reward)
            if ddqn_loss is not None:
                ddqn_losses.append(ddqn_loss)
            if dueling_loss is not None:
                dueling_losses.append(dueling_loss)

            # 衰减epsilon
            epsilon = max(EPSILON_END, epsilon * EPSILON_DECAY)

            # 定期更新目标网络
            if episode % TARGET_UPDATE == 0:
                ddqn_target_net.load_state_dict(ddqn_net.state_dict())
                dueling_target_net.load_state_dict(dueling_net.state_dict())

            # 输出训练结果
            if episode % 50 == 0:  # 每50个episode输出一次
                ddqn_loss_str = f"{ddqn_loss:.4f}" if ddqn_loss is not None else "N/A"
                dueling_loss_str = f"{dueling_loss:.4f}" if dueling_loss is not None else "N/A"
                print(f"Episode {episode}, DDQN Loss: {ddqn_loss_str}, Dueling Loss: {dueling_loss_str}")
                print(f"  DDQN Reward: {ddqn_episode_reward:.2f}, Dueling Reward: {dueling_episode_reward:.2f}, Epsilon: {epsilon:.3f}")

    except KeyboardInterrupt:
        print("\n训练被用户中断")
    except Exception as e:
        print(f"\n训练过程中出现错误: {e}")
    finally:
        print(f"\n训练完成! 共完成 {len(ddqn_rewards)} 个回合")

        # 创建比较可视化
        if len(ddqn_rewards) > 0:
            create_comparison_visualization(ddqn_rewards, dueling_rewards, ddqn_losses, dueling_losses)
        else:
            print("没有训练数据，跳过可视化")

if __name__ == "__main__":
    main()