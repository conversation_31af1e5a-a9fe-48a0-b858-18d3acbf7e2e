import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import Slider, Button
import matplotlib.animation as animation
import math
from matplotlib.patches import Circle
import tkinter as tk
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure

#显示中文
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class UAVTransmissionVisualizer:
    def __init__(self):
        # 系统参数
        self.uav_speed = 20  # m/s
        self.fc = 2.4e9      # 载波频率 Hz
        self.Pm = 1.0        # 地面用户发射功率 W
        self.Pu = 1.0        # 无人机发射功率 W
        self.Bgt = 1e6       # 地面用户带宽 Hz
        self.Bu = 1e6        # 无人机带宽 Hz
        self.sigma_squared = 1e-12  # 噪声功率 W
        
        # 初始参数
        self.radius = 100
        self.angle1 = 30
        self.angle2 = 150
        self.uav_height = 50
        self.time_progress = 0
        
        # 动画相关
        self.is_animating = False
        self.animation_speed = 0.01
        
        self.setup_gui()
        
    def calculate_uav_transmission_rates(self, h_u, d_m, f_c=2.4e9, 
                                       P_m=1.0, P_u=1.0, B_gt=1e6, B_u=1e6, 
                                       sigma_squared=1e-12, c=3e8, 
                                       a=9.61, b=0.16, zeta_los=1.0, zeta_nlos=20.0):
        """计算无人机与地面用户的上下行链路传输速率"""
        # 计算俯仰角 (度)
        theta_m = math.asin(h_u / d_m) * 180 / math.pi
        
        # 计算LoS概率
        P_r_los = 1 / (1 + a * math.exp(-b * (theta_m - a)))
        P_r_nlos = 1 - P_r_los
        
        # 计算基本路径损耗 (dB)
        basic_path_loss = 20 * math.log10(4 * math.pi * f_c * d_m / c)
        
        # 计算LoS和NLoS路径损耗 (dB)
        PL_los = basic_path_loss + zeta_los
        PL_nlos = basic_path_loss + zeta_nlos
        
        # 计算平均路径损耗 (dB)
        PL_m = P_r_los * PL_los + P_r_nlos * PL_nlos
        
        # 将路径损耗转换为线性值
        path_loss_linear = 10 ** (-PL_m / 10)
        
        # 计算上行链路传输速率 (bps)
        uplink_snr = (P_m * path_loss_linear) / sigma_squared
        uplink_rate = B_gt * math.log2(1 + uplink_snr)
        
        # 计算下行链路传输速率 (bps)
        downlink_snr = (P_u * path_loss_linear) / sigma_squared
        downlink_rate = B_u * math.log2(1 + downlink_snr)
        
        return {
            'uplink_rate': uplink_rate,
            'downlink_rate': downlink_rate,
            'path_loss_db': PL_m,
            'los_probability': P_r_los,
            'nlos_probability': P_r_nlos,
            'pitch_angle': theta_m
        }
    
    def setup_gui(self):
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("无人机传输速率实时可视化系统")
        self.root.geometry("1200x800")
        
        # 创建上下分割的主框架
        top_frame = tk.Frame(self.root, bg='white')
        top_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True)
        
        bottom_frame = tk.Frame(self.root, bg='lightblue', height=150)
        bottom_frame.pack(side=tk.BOTTOM, fill=tk.X)
        bottom_frame.pack_propagate(False)
        
        # 创建matplotlib图形
        self.fig = Figure(figsize=(12, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, master=top_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 创建子图
        self.ax1 = self.fig.add_subplot(221)  # 主视图
        self.ax2 = self.fig.add_subplot(222)  # 传输速率图
        self.ax3 = self.fig.add_subplot(223)  # 参数随时间变化
        self.ax4 = self.fig.add_subplot(224)  # 路径损耗图
        
        # 创建控制面板
        self.create_control_panel(bottom_frame)
        
        # 初始化图形
        self.update_plots()
        
    def create_control_panel(self, parent):
        # 标题
        title = tk.Label(parent, text="控制面板", font=("Arial", 16, "bold"), 
                        bg='lightblue', fg='darkblue')
        title.pack(pady=5)
        
        # 主控制框架
        main_control = tk.Frame(parent, bg='lightblue')
        main_control.pack(fill=tk.BOTH, expand=True, padx=10)
        
        # 第一行
        row1 = tk.Frame(main_control, bg='lightblue')
        row1.pack(fill=tk.X, pady=2)
        
        tk.Label(row1, text="半径:", bg='lightblue', font=("Arial", 10)).grid(row=0, column=0, sticky='w')
        self.radius_scale = tk.Scale(row1, from_=50, to=300, orient=tk.HORIZONTAL,
                                   command=self.update_radius, length=150, bg='lightblue')
        self.radius_scale.set(self.radius)
        self.radius_scale.grid(row=0, column=1, padx=5)
        
        tk.Label(row1, text="角度1:", bg='lightblue', font=("Arial", 10)).grid(row=0, column=2, sticky='w', padx=(20,0))
        self.angle1_scale = tk.Scale(row1, from_=0, to=360, orient=tk.HORIZONTAL,
                                   command=self.update_angle1, length=150, bg='lightblue')
        self.angle1_scale.set(self.angle1)
        self.angle1_scale.grid(row=0, column=3, padx=5)
        
        # 第二行
        row2 = tk.Frame(main_control, bg='lightblue')
        row2.pack(fill=tk.X, pady=2)
        
        tk.Label(row2, text="角度2:", bg='lightblue', font=("Arial", 10)).grid(row=0, column=0, sticky='w')
        self.angle2_scale = tk.Scale(row2, from_=0, to=360, orient=tk.HORIZONTAL,
                                   command=self.update_angle2, length=150, bg='lightblue')
        self.angle2_scale.set(self.angle2)
        self.angle2_scale.grid(row=0, column=1, padx=5)
        
        tk.Label(row2, text="高度:", bg='lightblue', font=("Arial", 10)).grid(row=0, column=2, sticky='w', padx=(20,0))
        self.height_scale = tk.Scale(row2, from_=10, to=200, orient=tk.HORIZONTAL,
                                   command=self.update_height, length=150, bg='lightblue')
        self.height_scale.set(self.uav_height)
        self.height_scale.grid(row=0, column=3, padx=5)
        
        # 第三行
        row3 = tk.Frame(main_control, bg='lightblue')
        row3.pack(fill=tk.X, pady=2)
        
        tk.Label(row3, text="时间:", bg='lightblue', font=("Arial", 10)).grid(row=0, column=0, sticky='w')
        self.time_scale = tk.Scale(row3, from_=0, to=1, orient=tk.HORIZONTAL,
                                 command=self.update_time, length=200, resolution=0.01, bg='lightblue')
        self.time_scale.set(self.time_progress)
        self.time_scale.grid(row=0, column=1, padx=5)
        
        self.animate_button = tk.Button(row3, text="开始动画", command=self.toggle_animation)
        self.animate_button.grid(row=0, column=2, padx=10)
        
        reset_button = tk.Button(row3, text="重置", command=self.reset_parameters)
        reset_button.grid(row=0, column=3, padx=5)
        
    def update_radius(self, val):
        self.radius = float(val)
        self.update_plots()
        
    def update_angle1(self, val):
        self.angle1 = float(val)
        self.update_plots()
        
    def update_angle2(self, val):
        self.angle2 = float(val)
        self.update_plots()
        
    def update_height(self, val):
        self.uav_height = float(val)
        self.update_plots()
        
    def update_time(self, val):
        self.time_progress = float(val)
        self.update_plots()
        
    def toggle_animation(self):
        if not self.is_animating:
            self.is_animating = True
            self.animate_button.config(text="停止动画")
            self.animate()
        else:
            self.is_animating = False
            self.animate_button.config(text="开始动画")
            
    def animate(self):
        if self.is_animating:
            self.time_progress += self.animation_speed
            if self.time_progress >= 1:
                self.time_progress = 0
            self.time_scale.set(self.time_progress)
            self.update_plots()
            self.root.after(50, self.animate)
            
    def reset_parameters(self):
        self.radius = 100
        self.angle1 = 30
        self.angle2 = 150
        self.uav_height = 50
        self.time_progress = 0
        
        self.radius_scale.set(self.radius)
        self.angle1_scale.set(self.angle1)
        self.angle2_scale.set(self.angle2)
        self.height_scale.set(self.uav_height)
        self.time_scale.set(self.time_progress)
        
        self.update_plots()
        
    def update_plots(self):
        try:
            # 清除所有子图
            for ax in [self.ax1, self.ax2, self.ax3, self.ax4]:
                ax.clear()
                
            # 计算点的位置
            point1 = np.array([self.radius * np.cos(np.radians(self.angle1)),
                              self.radius * np.sin(np.radians(self.angle1))])
            point2 = np.array([self.radius * np.cos(np.radians(self.angle2)),
                              self.radius * np.sin(np.radians(self.angle2))])
            
            # 计算当前无人机位置
            current_pos = point1 + self.time_progress * (point2 - point1)
            
            # 计算传输参数
            distance_from_center = np.linalg.norm(current_pos)
            d_m = np.sqrt(distance_from_center**2 + self.uav_height**2)
            
            rates = self.calculate_uav_transmission_rates(
                self.uav_height, d_m, self.fc, self.Pm, self.Pu, 
                self.Bgt, self.Bu, self.sigma_squared
            )
            
            # 绘制主视图
            self.plot_main_view(point1, point2, current_pos)
            
            # 绘制传输速率图
            self.plot_transmission_rates(rates)
            
            # 绘制参数随时间变化
            self.plot_parameter_trends()
            
            # 绘制路径损耗图
            self.plot_path_loss_analysis()
            
            # 更新画布
            self.canvas.draw()
            
        except Exception as e:
            print(f"更新图形时出错: {e}")
            
    def plot_main_view(self, point1, point2, current_pos):
        # 绘制圆形边界
        circle = Circle((0, 0), self.radius, fill=False, color='gray', linestyle='--')
        self.ax1.add_patch(circle)
        
        # 绘制圆心（用户位置）
        self.ax1.plot(0, 0, 'ro', markersize=8, label='用户位置')
        
        # 绘制起始点和终点
        self.ax1.plot(point1[0], point1[1], 'bo', markersize=6, label='点1')
        self.ax1.plot(point2[0], point2[1], 'go', markersize=6, label='点2')
        
        # 绘制连接线
        self.ax1.plot([point1[0], point2[0]], [point1[1], point2[1]], 'k-', linewidth=2)
        
        # 绘制当前无人机位置
        self.ax1.plot(current_pos[0], current_pos[1], 'o', color='orange', markersize=8, label='无人机')
        
        # 绘制从圆心到无人机的连线
        self.ax1.plot([0, current_pos[0]], [0, current_pos[1]], 'r--', alpha=0.7)
        
        # 设置坐标轴
        limit = self.radius * 1.2
        self.ax1.set_xlim(-limit, limit)
        self.ax1.set_ylim(-limit, limit)
        self.ax1.set_aspect('equal')
        self.ax1.grid(True, alpha=0.3)
        self.ax1.set_xlabel('距离 (m)')
        self.ax1.set_ylabel('距离 (m)')
        self.ax1.set_title('无人机飞行路径')
        self.ax1.legend()
        
    def plot_transmission_rates(self, rates):
        # 绘制传输速率柱状图
        categories = ['上行速率', '下行速率']
        values = [rates['uplink_rate']/1e6, rates['downlink_rate']/1e6]
        colors = ['blue', 'red']
        
        bars = self.ax2.bar(categories, values, color=colors, alpha=0.7)
        self.ax2.set_ylabel('速率 (Mbps)')
        self.ax2.set_title('传输速率')
        self.ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            self.ax2.text(bar.get_x() + bar.get_width()/2., height,
                         f'{value:.2f}', ha='center', va='bottom')
                         
    def plot_parameter_trends(self):
        # 简化的参数趋势图
        self.ax3.text(0.5, 0.5, f'半径: {self.radius}m\n角度1: {self.angle1}°\n角度2: {self.angle2}°\n高度: {self.uav_height}m\n时间: {self.time_progress:.2f}',
                     ha='center', va='center', transform=self.ax3.transAxes, fontsize=12)
        self.ax3.set_title('当前参数')
        
    def plot_path_loss_analysis(self):
        # 简化的路径损耗分析
        self.ax4.text(0.5, 0.5, '路径损耗分析\n(简化显示)', 
                     ha='center', va='center', transform=self.ax4.transAxes, fontsize=12)
        self.ax4.set_title('路径损耗')
        
    def run(self):
        print("启动无人机传输速率可视化系统...")
        self.root.mainloop()

if __name__ == "__main__":
    visualizer = UAVTransmissionVisualizer()
    visualizer.run()
