import tkinter as tk
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure

class SimpleControlTest:
    def __init__(self):
        self.radius = 100
        self.angle1 = 30
        self.angle2 = 150
        self.height = 50
        self.time_progress = 0
        
        self.setup_gui()
        
    def setup_gui(self):
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("控制面板测试")
        self.root.geometry("800x600")
        
        # 创建matplotlib图形
        self.fig = Figure(figsize=(8, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.root)
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)
        
        # 创建子图
        self.ax = self.fig.add_subplot(111)
        
        # 创建控制面板
        self.create_control_panel()
        
        # 初始化图形
        self.update_plot()
        
    def create_control_panel(self):
        # 控制面板框架
        control_frame = tk.Frame(self.root)
        control_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)
        
        # 半径控制
        tk.Label(control_frame, text="半径:").grid(row=0, column=0, sticky='w')
        self.radius_scale = tk.Scale(control_frame, from_=50, to=300, orient=tk.HORIZONTAL, 
                                   command=self.update_radius, length=200)
        self.radius_scale.set(self.radius)
        self.radius_scale.grid(row=0, column=1, padx=5)
        
        # 角度1控制
        tk.Label(control_frame, text="角度1:").grid(row=1, column=0, sticky='w')
        self.angle1_scale = tk.Scale(control_frame, from_=0, to=360, orient=tk.HORIZONTAL, 
                                   command=self.update_angle1, length=200)
        self.angle1_scale.set(self.angle1)
        self.angle1_scale.grid(row=1, column=1, padx=5)
        
        # 角度2控制
        tk.Label(control_frame, text="角度2:").grid(row=2, column=0, sticky='w')
        self.angle2_scale = tk.Scale(control_frame, from_=0, to=360, orient=tk.HORIZONTAL, 
                                   command=self.update_angle2, length=200)
        self.angle2_scale.set(self.angle2)
        self.angle2_scale.grid(row=2, column=1, padx=5)
        
        # 高度控制
        tk.Label(control_frame, text="高度:").grid(row=3, column=0, sticky='w')
        self.height_scale = tk.Scale(control_frame, from_=10, to=200, orient=tk.HORIZONTAL, 
                                   command=self.update_height, length=200)
        self.height_scale.set(self.height)
        self.height_scale.grid(row=3, column=1, padx=5)
        
        # 时间进度控制
        tk.Label(control_frame, text="时间:").grid(row=4, column=0, sticky='w')
        self.time_scale = tk.Scale(control_frame, from_=0, to=1, orient=tk.HORIZONTAL, 
                                 command=self.update_time, length=200, resolution=0.01)
        self.time_scale.set(self.time_progress)
        self.time_scale.grid(row=4, column=1, padx=5)
        
        print("简化控制面板创建成功")
        
    def update_radius(self, val):
        self.radius = float(val)
        print(f"半径更新为: {self.radius}")
        self.update_plot()
        
    def update_angle1(self, val):
        self.angle1 = float(val)
        print(f"角度1更新为: {self.angle1}")
        self.update_plot()
        
    def update_angle2(self, val):
        self.angle2 = float(val)
        print(f"角度2更新为: {self.angle2}")
        self.update_plot()
        
    def update_height(self, val):
        self.height = float(val)
        print(f"高度更新为: {self.height}")
        self.update_plot()
        
    def update_time(self, val):
        self.time_progress = float(val)
        print(f"时间进度更新为: {self.time_progress}")
        self.update_plot()
        
    def update_plot(self):
        try:
            self.ax.clear()
            
            # 计算点的位置
            point1 = np.array([self.radius * np.cos(np.radians(self.angle1)),
                              self.radius * np.sin(np.radians(self.angle1))])
            point2 = np.array([self.radius * np.cos(np.radians(self.angle2)),
                              self.radius * np.sin(np.radians(self.angle2))])
            
            # 计算当前位置
            current_pos = point1 + self.time_progress * (point2 - point1)
            
            # 绘制圆形
            circle = plt.Circle((0, 0), self.radius, fill=False, color='gray', linestyle='--')
            self.ax.add_patch(circle)
            
            # 绘制点
            self.ax.plot(0, 0, 'ro', markersize=8, label='中心')
            self.ax.plot(point1[0], point1[1], 'bo', markersize=6, label='点1')
            self.ax.plot(point2[0], point2[1], 'go', markersize=6, label='点2')
            self.ax.plot(current_pos[0], current_pos[1], 'o', color='orange', markersize=8, label='当前位置')
            
            # 绘制连线
            self.ax.plot([point1[0], point2[0]], [point1[1], point2[1]], 'k-', linewidth=2)
            self.ax.plot([0, current_pos[0]], [0, current_pos[1]], 'r--', alpha=0.7)
            
            # 设置坐标轴
            limit = self.radius * 1.2
            self.ax.set_xlim(-limit, limit)
            self.ax.set_ylim(-limit, limit)
            self.ax.set_aspect('equal')
            self.ax.grid(True, alpha=0.3)
            self.ax.set_title(f'控制测试 - 半径:{self.radius}, 高度:{self.height}')
            self.ax.legend()
            
            # 更新画布
            self.canvas.draw()
            
        except Exception as e:
            print(f"更新图形时出错: {e}")
            
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    test = SimpleControlTest()
    test.run()
