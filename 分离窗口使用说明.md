# 无人机传输速率可视化系统 - 分离窗口版使用说明

## 🎯 功能概述

本系统将控制面板和图形显示完全分离为两个独立窗口，提供更灵活的用户体验：

### 📊 图形显示窗口
- **窗口标题**: "无人机传输速率可视化 - 图形显示"
- **位置**: 屏幕左侧 (100, 100)
- **尺寸**: 1000×700 像素
- **内容**: 四个实时更新的图表区域

### 🎛️ 控制面板窗口
- **窗口标题**: "无人机传输速率可视化 - 控制面板"
- **位置**: 屏幕右侧 (1120, 100)
- **尺寸**: 600×500 像素
- **内容**: 所有参数控制滑块和按钮

## 🚀 启动方式

### 推荐版本
```bash
python 5_separated.py
```
这是专门为分离窗口优化的版本，界面更美观，功能更稳定。

### 原版本（已修改）
```bash
python 5.py
```
原版本也已支持分离窗口，但界面相对简单。

## 📋 界面布局

### 图形显示窗口（左侧）
```
┌─────────────────────────────────────┐
│  左上: 无人机飞行路径图              │
│  右上: 实时传输速率柱状图            │
├─────────────────────────────────────┤
│  左下: 当前参数显示                 │
│  右下: 路径损耗分析                 │
└─────────────────────────────────────┘
```

### 控制面板窗口（右侧）
```
┌─────────────────────────────────────┐
│           🎛️ 控制面板 🎛️            │
│    拖动滑块调节参数，观察图形变化     │
├─────────────────────────────────────┤
│  圆形半径 (m):    [滑块]    100m    │
│  点1角度 (°):     [滑块]     30°    │
│  点2角度 (°):     [滑块]    150°    │
│  无人机高度 (m):  [滑块]     50m    │
│  时间进度:        [滑块]    0.00    │
├─────────────────────────────────────┤
│  [开始动画] [重置参数]              │
└─────────────────────────────────────┘
```

## 🎮 控制功能

### 参数调节滑块
1. **圆形半径** (50-300m)
   - 调节无人机飞行区域的大小
   - 实时显示当前数值

2. **点1角度** (0-360°)
   - 设置起始点的角度位置
   - 以圆心为原点的极坐标角度

3. **点2角度** (0-360°)
   - 设置终点的角度位置
   - 无人机将在两点间飞行

4. **无人机高度** (10-200m)
   - 调节无人机的飞行高度
   - 影响传输特性和路径损耗

5. **时间进度** (0-1)
   - 控制无人机在路径上的位置
   - 0=起始点，1=终点，0.5=中点

### 控制按钮
- **开始动画**: 自动播放无人机飞行过程
  - 点击后变为"停止动画"
  - 按钮颜色变化指示状态
- **重置参数**: 恢复所有参数到默认值

## 🔄 实时同步

- 拖动控制面板中的任何滑块
- 图形窗口中的所有图表立即更新
- 参数数值实时显示在滑块旁边
- 动画过程中时间进度自动更新

## 🖥️ 窗口管理

### 窗口位置
- 两个窗口自动定位，避免重叠
- 图形窗口在左，控制面板在右
- 可以手动拖动调整位置

### 窗口关闭
- **关闭图形窗口**: 整个程序退出
- **关闭控制面板**: 仅隐藏控制面板，程序继续运行

### 多显示器支持
- 可以将两个窗口拖到不同显示器
- 适合大屏幕或多屏幕工作环境

## 📊 显示内容详解

### 左上图：无人机飞行路径
- 圆形边界：飞行区域
- 红点：用户位置（圆心）
- 蓝点：起始点（点1）
- 绿点：终点（点2）
- 橙点：当前无人机位置
- 虚线：无人机到用户的连线

### 右上图：传输速率
- 蓝色柱：上行链路速率
- 红色柱：下行链路速率
- 数值标签：精确的Mbps值

### 左下图：当前参数
- 实时显示所有参数的当前值
- 蓝色背景框，便于阅读

### 右下图：路径损耗分析
- 路径损耗值（dB）
- LoS（视距）概率
- 俯仰角度

## 💡 使用技巧

1. **参数调节**: 先调节半径和高度，再调节角度
2. **观察变化**: 注意传输速率随位置的变化规律
3. **动画演示**: 使用动画功能观察完整飞行过程
4. **窗口布局**: 根据屏幕大小调整两个窗口位置
5. **数据分析**: 结合四个图表全面分析传输性能

## 🔧 技术特点

- **独立窗口**: 图形和控制完全分离
- **实时更新**: 毫秒级响应参数变化
- **美观界面**: 现代化的GUI设计
- **稳定性强**: 完善的错误处理机制
- **易于使用**: 直观的操作界面

## 📝 注意事项

1. 确保屏幕分辨率足够显示两个窗口
2. 建议屏幕宽度至少1600像素
3. 如果窗口位置不合适，可以手动拖动调整
4. 动画运行时可以随时调节其他参数
5. 重置功能会停止当前动画

这种分离窗口的设计让用户可以更灵活地使用系统，特别适合需要频繁调节参数并观察结果的场景。
