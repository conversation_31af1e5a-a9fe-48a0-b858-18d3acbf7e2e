from itertools import chain
import numpy as np
global hot_type,uav_z
uav_z = 100
hot_type=0 
hot_type=-1
class UAVNetwork:
    LIGHT_SPEED = 3e8  
    ETA_LOS = 1  
    ETA_NLOS = 20  
    def __init__(
        self,
        user_coordinates: dict,  
        uav_coordinates: list,  
        subchannels: dict,  
        users_transmit_power: dict,  
        uav_transmit_power: list,  
        carrier_frequency: float = 2e9,  
        noise_power: float = -174,  
        channels_per_uav: int = 30,  
        down_bandwidth: float = 1e6,  
        up_bandwidth: float = 10e6,  
    ):
        self.user_coordinates = self.flatten_dict(user_coordinates)  
        self.uav_coordinates = np.array(uav_coordinates)  
        self.check_coordinates()  
        self.subchannels = self.flatten_dict(subchannels)  
        self.users_transmit_power = self.flatten_dict(users_transmit_power)  
        self.labels = self.generate_labels(user_coordinates)  
        self.uav_transmit_power = np.array(uav_transmit_power)  
        self.carrier_frequency = carrier_frequency  
        self.noise_power = noise_power  
        self.channels_per_uav = channels_per_uav  
        self.down_bandwidth = down_bandwidth  
        self.up_bandwidth = up_bandwidth  
        self.sub_bandwidth = self.up_bandwidth / self.channels_per_uav  
        self.noise_power_density_user = 10 ** (self.noise_power  / 10) * self.sub_bandwidth
        self.noise_power_density_uav = 10 ** (self.noise_power  / 10) * self.down_bandwidth
    def flatten_dict(self, data):
        if isinstance(data, dict):
            return np.array(list(chain(*data.values())))  
        elif isinstance(data, np.ndarray):
            return data.flatten()  
        elif isinstance(data, list):
            return np.array(
                list(chain(*[self.flatten_dict(item) if isinstance(item, list) else [item] for item in data]))
            ).flatten()  
        else:
            raise TypeError("Unsupported type: must be either a dictionary, a numpy.ndarray, or a list")
    def generate_labels(self, user_coordinates: dict) -> np.ndarray:
        labels = []  
        for i, (cluster, coords) in enumerate(user_coordinates.items()):
            labels.extend([i] * len(coords))  
        return np.array(labels)  
    def check_coordinates(self):
        if self.user_coordinates.shape[1] == 2:
            self.user_coordinates = np.pad(self.user_coordinates, ((0, 0), (0, 1)), "constant")  
        if self.uav_coordinates.ndim == 1 and self.uav_coordinates.size == 2:
            self.uav_coordinates = np.append(self.uav_coordinates, 100)  
        elif self.uav_coordinates.ndim == 2 and self.uav_coordinates.shape[1] == 2:
            self.uav_coordinates = np.pad(
                self.uav_coordinates, ((0, 0), (0, 1)), "constant", constant_values=100
            )  
    def calculate_distance(self, user_coords: np.ndarray, uav_coords: np.ndarray) -> float:
        return np.linalg.norm(user_coords - uav_coords)  
    def calculate_elevation_angle(self, uav_coords: np.ndarray, user_coords: np.ndarray) -> float:
        delta = uav_coords - user_coords  
        return np.degrees(np.arctan2(delta[2], np.linalg.norm(delta[:2])))  
    def calculate_path_loss(self, user_coords: np.ndarray, uav_coords: np.ndarray) -> float:
        a, b = 9.61, 0.16  
        distance = self.calculate_distance(user_coords, uav_coords)  
        theta = self.calculate_elevation_angle(uav_coords, user_coords)  
        prob_los = 1 / (1 + a * np.exp(-b * (theta - a)))  
        l_los = (
            20 * np.log10(4 * np.pi * self.carrier_frequency * distance / self.LIGHT_SPEED) + self.ETA_LOS
        )  
        l_nlos = l_los - self.ETA_LOS + self.ETA_NLOS  
        return prob_los * l_los + (1 - prob_los) * l_nlos  

