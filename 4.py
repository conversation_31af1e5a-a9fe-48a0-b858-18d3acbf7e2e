import numpy as np
import math
import matplotlib.pyplot as plt


#显示中文
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def calculate_uav_transmission_rates(
    # 位置参数
    h_u,           # 无人机高度 (m)
    d_m,           # 无人机与地面用户m的距离 (m)    
    # 系统参数
    f_c,           # 载波频率 (Hz)    
    # 传输参数
    P_m,           # 地面用户m的发射功率 (W)
    P_u,           # 无人机发射功率 (W)
    B_gt,          # 地面用户带宽 (Hz)
    B_u,           # 无人机带宽 (Hz)
    sigma_squared,  # 高斯白噪声功率 (W)

    c=3e8,         # 光速 (m/s)
    
    # 环境参数
    a=9.61,        # 环境变量a (默认城市环境)
    b=0.16,        # 环境变量b (默认城市环境)
    zeta_los=1.0,  # LoS额外自由空间损耗 (dB)
    zeta_nlos=20.0,# NLoS额外自由空间损耗 (dB)
):
    """
    计算无人机与地面用户的上下行链路传输速率
    
    参数说明:
    - h_u: 无人机高度
    - d_m: 无人机与地面用户的距离
    - f_c: 载波频率
    - c: 光速
    - a, b: 环境参数
    - zeta_los, zeta_nlos: LoS和NLoS环境的额外损耗
    - P_m: 地面用户发射功率
    - P_u: 无人机发射功率
    - B_gt: 地面用户带宽
    - B_u: 无人机带宽
    - sigma_squared: 噪声功率
    
    返回:
    - uplink_rate: 上行链路传输速率 (bps)
    - downlink_rate: 下行链路传输速率 (bps)
    - path_loss: 平均路径损耗 (dB)
    - los_prob: 视距概率
    """
    
    # 计算俯仰角 (度)
    theta_m = math.asin(h_u / d_m) * 180 / math.pi
    
    # 计算LoS概率
    P_r_los = 1 / (1 + a * math.exp(-b * (theta_m - a)))
    
    # 计算NLoS概率
    P_r_nlos = 1 - P_r_los
    
    # 计算基本路径损耗 (dB)
    basic_path_loss = 20 * math.log10(4 * math.pi * f_c * d_m / c)
    
    # 计算LoS和NLoS路径损耗 (dB)
    PL_los = basic_path_loss + zeta_los
    PL_nlos = basic_path_loss + zeta_nlos
    
    # 计算平均路径损耗 (dB)
    PL_m = P_r_los * PL_los + P_r_nlos * PL_nlos
    
    # 将路径损耗转换为线性值
    path_loss_linear = 10 ** (-PL_m / 10)
    
    # 计算上行链路传输速率 (bps)
    uplink_snr = (P_m * path_loss_linear) / sigma_squared
    uplink_rate = B_gt * math.log2(1 + uplink_snr)
    
    # 计算下行链路传输速率 (bps)
    downlink_snr = (P_u * path_loss_linear) / sigma_squared
    downlink_rate = B_u * math.log2(1 + downlink_snr)
    
    return {
        'uplink_rate': uplink_rate,
        'downlink_rate': downlink_rate,
        'path_loss_db': PL_m,
        'los_probability': P_r_los,
        'nlos_probability': P_r_nlos,
        'pitch_angle': theta_m
    }

# 示例使用
if __name__ == "__main__":
    # 示例参数
    result = calculate_uav_transmission_rates(
        h_u=100,              # 无人机高度 100m
        d_m=600,              # 距离 600m
        f_c=2.4e9,            # 载波频率 2.4GHz
        P_m=0.01,             # 地面用户发射功率 0.01W
        P_u=0.1,              # 无人机发射功率 0.1W
        B_gt=1e6,             # 地面用户带宽 1MHz
        B_u=1e6,              # 无人机带宽 1MHz
        sigma_squared=1e-12   # 噪声功率 -90dBm
    )
    print(f"上行链路传输速率: {result['uplink_rate']/1e6:.2f} Mbps")
    print(f"下行链路传输速率: {result['downlink_rate']/1e6:.2f} Mbps")
    print(f"路径损耗: {result['path_loss_db']:.2f} dB")
    print(f"视距概率: {result['los_probability']:.3f}")
    print(f"俯仰角: {result['pitch_angle']:.2f} 度")

    # 增加趋势图：无人机与用户距离和传输速率的变化
    import matplotlib.pyplot as plt
    distances = np.linspace(100, 2000, 50)  # 距离从100m到2000m
    uplink_rates = []
    downlink_rates = []
    for d in distances:
        res = calculate_uav_transmission_rates(
            h_u=100,
            d_m=d,
            f_c=2.4e9,
            P_m=0.01,
            P_u=0.1,
            B_gt=1e6,
            B_u=1e6,
            sigma_squared=1e-12
        )
        uplink_rates.append(res['uplink_rate'] / 1e6)  # Mbps
        downlink_rates.append(res['downlink_rate'] / 1e6)  # Mbps

    plt.figure(figsize=(8,5))
    plt.plot(distances, uplink_rates, label='上行链路速率 (Mbps)')
    plt.plot(distances, downlink_rates, label='下行链路速率 (Mbps)')
    plt.xlabel('无人机与用户距离 (m)')
    plt.ylabel('传输速率 (Mbps)')
    plt.title('无人机与用户距离-传输速率趋势')
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.show()