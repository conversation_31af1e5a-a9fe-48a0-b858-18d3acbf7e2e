import tkinter as tk

class DebugControls:
    def __init__(self):
        self.value1 = 50
        self.value2 = 100
        
        self.root = tk.Tk()
        self.root.title("调试控制面板")
        self.root.geometry("400x300")
        
        # 创建标签显示当前值
        self.label1 = tk.Label(self.root, text=f"值1: {self.value1}")
        self.label1.pack(pady=10)
        
        self.label2 = tk.Label(self.root, text=f"值2: {self.value2}")
        self.label2.pack(pady=10)
        
        # 创建滑块1
        tk.Label(self.root, text="滑块1:").pack()
        self.scale1 = tk.Scale(self.root, from_=0, to=100, orient=tk.HORIZONTAL,
                              command=self.update_value1, length=300)
        self.scale1.set(self.value1)
        self.scale1.pack(pady=5)
        
        # 创建滑块2
        tk.Label(self.root, text="滑块2:").pack()
        self.scale2 = tk.Scale(self.root, from_=0, to=200, orient=tk.HORIZONTAL,
                              command=self.update_value2, length=300)
        self.scale2.set(self.value2)
        self.scale2.pack(pady=5)
        
        # 测试按钮
        test_btn = tk.Button(self.root, text="测试设置值", command=self.test_set_values)
        test_btn.pack(pady=10)
        
        print("调试控制面板创建完成")
        
    def update_value1(self, val):
        self.value1 = float(val)
        self.label1.config(text=f"值1: {self.value1}")
        print(f"滑块1更新: {self.value1}")
        
    def update_value2(self, val):
        self.value2 = float(val)
        self.label2.config(text=f"值2: {self.value2}")
        print(f"滑块2更新: {self.value2}")
        
    def test_set_values(self):
        print("测试设置值...")
        self.scale1.set(25)
        self.scale2.set(150)
        print("设置完成")
        
    def run(self):
        print("启动调试界面...")
        self.root.mainloop()
        print("调试界面已关闭")

if __name__ == "__main__":
    debug = DebugControls()
    debug.run()
