import numpy as np
import matplotlib.pyplot as plt
import math
from matplotlib.patches import Circle
import tkinter as tk
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure

#显示中文
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class UAVTransmissionVisualizer:
    def __init__(self):
        # 系统参数
        self.uav_speed = 20  # m/s
        self.fc = 2.4e9      # 载波频率 Hz
        self.Pm = 1.0        # 地面用户发射功率 W
        self.Pu = 1.0        # 无人机发射功率 W
        self.Bgt = 1e6       # 地面用户带宽 Hz
        self.Bu = 1e6        # 无人机带宽 Hz
        self.sigma_squared = 1e-12  # 噪声功率 W
        
        # 初始参数
        self.radius = 100
        self.angle1 = 30
        self.angle2 = 150
        self.uav_height = 50
        self.time_progress = 0
        
        # 动画相关
        self.is_animating = False
        self.animation_speed = 0.01
        
        self.setup_gui()
        
    def calculate_uav_transmission_rates(self, h_u, d_m, f_c=2.4e9, 
                                       P_m=1.0, P_u=1.0, B_gt=1e6, B_u=1e6, 
                                       sigma_squared=1e-12, c=3e8, 
                                       a=9.61, b=0.16, zeta_los=1.0, zeta_nlos=20.0):
        """计算无人机与地面用户的上下行链路传输速率"""
        # 计算俯仰角 (度)
        theta_m = math.asin(h_u / d_m) * 180 / math.pi
        
        # 计算LoS概率
        P_r_los = 1 / (1 + a * math.exp(-b * (theta_m - a)))
        P_r_nlos = 1 - P_r_los
        
        # 计算基本路径损耗 (dB)
        basic_path_loss = 20 * math.log10(4 * math.pi * f_c * d_m / c)
        
        # 计算LoS和NLoS路径损耗 (dB)
        PL_los = basic_path_loss + zeta_los
        PL_nlos = basic_path_loss + zeta_nlos
        
        # 计算平均路径损耗 (dB)
        PL_m = P_r_los * PL_los + P_r_nlos * PL_nlos
        
        # 将路径损耗转换为线性值
        path_loss_linear = 10 ** (-PL_m / 10)
        
        # 计算上行链路传输速率 (bps)
        uplink_snr = (P_m * path_loss_linear) / sigma_squared
        uplink_rate = B_gt * math.log2(1 + uplink_snr)
        
        # 计算下行链路传输速率 (bps)
        downlink_snr = (P_u * path_loss_linear) / sigma_squared
        downlink_rate = B_u * math.log2(1 + downlink_snr)
        
        return {
            'uplink_rate': uplink_rate,
            'downlink_rate': downlink_rate,
            'path_loss_db': PL_m,
            'los_probability': P_r_los,
            'nlos_probability': P_r_nlos,
            'pitch_angle': theta_m
        }
    
    def setup_gui(self):
        # 创建主窗口（图形显示窗口）
        self.root = tk.Tk()
        self.root.title("无人机传输速率可视化 - 图形显示")
        self.root.geometry("1000x700")
        self.root.configure(bg='white')
        
        # 设置主窗口位置
        self.root.geometry("+100+100")
        
        # 创建matplotlib图形
        self.fig = Figure(figsize=(10, 7))
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.root)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建子图
        self.ax1 = self.fig.add_subplot(221)  # 主视图
        self.ax2 = self.fig.add_subplot(222)  # 传输速率图
        self.ax3 = self.fig.add_subplot(223)  # 参数随时间变化
        self.ax4 = self.fig.add_subplot(224)  # 路径损耗图
        
        # 创建控制面板窗口
        self.create_control_window()
        
        # 初始化图形
        self.update_plots()
        
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_main_window_close)
        
    def create_control_window(self):
        """创建独立的控制面板窗口"""
        self.control_window = tk.Toplevel(self.root)
        self.control_window.title("无人机传输速率可视化 - 控制面板")
        self.control_window.geometry("600x500")
        self.control_window.configure(bg='lightblue')
        
        # 设置控制窗口位置（在主窗口右侧）
        self.control_window.geometry("+1120+100")
        
        # 设置控制窗口关闭事件
        self.control_window.protocol("WM_DELETE_WINDOW", self.on_control_window_close)
        
        self.create_control_panel()
        
    def create_control_panel(self):
        """创建控制面板内容"""
        # 标题
        title_label = tk.Label(self.control_window, text="🎛️ 控制面板 🎛️", 
                             font=("Arial", 18, "bold"), bg='lightblue', fg='darkblue')
        title_label.pack(pady=15)
        
        # 说明文字
        info_label = tk.Label(self.control_window, 
                            text="拖动滑块调节参数，观察图形窗口中的实时变化", 
                            font=("Arial", 11), bg='lightblue', fg='darkgreen')
        info_label.pack(pady=5)
        
        # 主控制框架
        control_frame = tk.Frame(self.control_window, bg='lightblue')
        control_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)
        
        # 半径控制
        self.create_control_row(control_frame, 0, "圆形半径 (m):", 
                               self.radius, 50, 300, self.update_radius, "m")
        
        # 角度1控制
        self.create_control_row(control_frame, 1, "点1角度 (°):", 
                               self.angle1, 0, 360, self.update_angle1, "°")
        
        # 角度2控制
        self.create_control_row(control_frame, 2, "点2角度 (°):", 
                               self.angle2, 0, 360, self.update_angle2, "°")
        
        # 高度控制
        self.create_control_row(control_frame, 3, "无人机高度 (m):", 
                               self.uav_height, 10, 200, self.update_height, "m")
        
        # 时间进度控制
        self.create_control_row(control_frame, 4, "时间进度:", 
                               self.time_progress, 0, 1, self.update_time, "", 0.01)
        
        # 按钮控制区域
        button_frame = tk.Frame(control_frame, bg='lightblue')
        button_frame.grid(row=5, column=0, columnspan=3, pady=30)
        
        # 动画控制按钮
        self.animate_button = tk.Button(button_frame, text="开始动画", 
                                      command=self.toggle_animation,
                                      font=("Arial", 12, "bold"), 
                                      width=12, height=2, bg='lightgreen')
        self.animate_button.pack(side=tk.LEFT, padx=10)

        # 重置按钮
        reset_button = tk.Button(button_frame, text="重置参数", 
                               command=self.reset_parameters,
                               font=("Arial", 12, "bold"), 
                               width=12, height=2, bg='lightyellow')
        reset_button.pack(side=tk.LEFT, padx=10)
        
    def create_control_row(self, parent, row, label_text, initial_value, 
                          from_val, to_val, command, unit, resolution=1):
        """创建一行控制组件"""
        # 标签
        tk.Label(parent, text=label_text, bg='lightblue', 
                font=("Arial", 12, "bold")).grid(row=row, column=0, sticky='w', pady=8)
        
        # 滑块
        scale = tk.Scale(parent, from_=from_val, to=to_val, orient=tk.HORIZONTAL,
                        command=command, length=250, bg='lightblue', 
                        resolution=resolution, font=("Arial", 10))
        scale.set(initial_value)
        scale.grid(row=row, column=1, padx=15, pady=8)
        
        # 数值显示标签
        if unit:
            value_text = f"{initial_value}{unit}"
        else:
            value_text = f"{initial_value:.2f}" if resolution < 1 else f"{initial_value}"
            
        value_label = tk.Label(parent, text=value_text, bg='lightblue', 
                              font=("Arial", 11), width=8)
        value_label.grid(row=row, column=2, padx=10)
        
        # 保存引用以便更新
        setattr(self, f"{label_text.split()[0].lower()}_scale", scale)
        setattr(self, f"{label_text.split()[0].lower()}_value_label", value_label)
        
    def update_radius(self, val):
        self.radius = float(val)
        if hasattr(self, 'radius_value_label'):
            self.radius_value_label.config(text=f"{self.radius}m")
        self.update_plots()
        
    def update_angle1(self, val):
        self.angle1 = float(val)
        if hasattr(self, 'angle1_value_label'):
            self.angle1_value_label.config(text=f"{self.angle1}°")
        self.update_plots()
        
    def update_angle2(self, val):
        self.angle2 = float(val)
        if hasattr(self, 'angle2_value_label'):
            self.angle2_value_label.config(text=f"{self.angle2}°")
        self.update_plots()
        
    def update_height(self, val):
        self.uav_height = float(val)
        if hasattr(self, 'height_value_label'):
            self.height_value_label.config(text=f"{self.uav_height}m")
        self.update_plots()
        
    def update_time(self, val):
        self.time_progress = float(val)
        if hasattr(self, 'time_value_label'):
            self.time_value_label.config(text=f"{self.time_progress:.2f}")
        self.update_plots()
        
    def toggle_animation(self):
        if not self.is_animating:
            self.is_animating = True
            self.animate_button.config(text="停止动画", bg='lightcoral')
            self.animate()
        else:
            self.is_animating = False
            self.animate_button.config(text="开始动画", bg='lightgreen')
            
    def animate(self):
        if self.is_animating:
            self.time_progress += self.animation_speed
            if self.time_progress >= 1:
                self.time_progress = 0
            if hasattr(self, 'time_scale'):
                self.time_scale.set(self.time_progress)
            self.update_plots()
            self.root.after(50, self.animate)
            
    def reset_parameters(self):
        self.radius = 100
        self.angle1 = 30
        self.angle2 = 150
        self.uav_height = 50
        self.time_progress = 0
        
        # 更新滑块
        if hasattr(self, 'radius_scale'):
            self.radius_scale.set(self.radius)
        if hasattr(self, 'angle1_scale'):
            self.angle1_scale.set(self.angle1)
        if hasattr(self, 'angle2_scale'):
            self.angle2_scale.set(self.angle2)
        if hasattr(self, 'height_scale'):
            self.height_scale.set(self.uav_height)
        if hasattr(self, 'time_scale'):
            self.time_scale.set(self.time_progress)
        
        self.update_plots()
        
    def update_plots(self):
        try:
            # 清除所有子图
            for ax in [self.ax1, self.ax2, self.ax3, self.ax4]:
                ax.clear()
                
            # 计算点的位置
            point1 = np.array([self.radius * np.cos(np.radians(self.angle1)),
                              self.radius * np.sin(np.radians(self.angle1))])
            point2 = np.array([self.radius * np.cos(np.radians(self.angle2)),
                              self.radius * np.sin(np.radians(self.angle2))])
            
            # 计算当前无人机位置
            current_pos = point1 + self.time_progress * (point2 - point1)
            
            # 计算传输参数
            distance_from_center = np.linalg.norm(current_pos)
            d_m = np.sqrt(distance_from_center**2 + self.uav_height**2)
            
            rates = self.calculate_uav_transmission_rates(
                self.uav_height, d_m, self.fc, self.Pm, self.Pu, 
                self.Bgt, self.Bu, self.sigma_squared
            )
            
            # 绘制主视图
            self.plot_main_view(point1, point2, current_pos)
            
            # 绘制传输速率图
            self.plot_transmission_rates(rates)
            
            # 绘制参数信息
            self.plot_parameters()
            
            # 绘制路径损耗信息
            self.plot_path_loss(rates)
            
            # 调整布局并更新画布
            self.fig.tight_layout()
            self.canvas.draw()
            
        except Exception as e:
            print(f"更新图形时出错: {e}")
            
    def plot_main_view(self, point1, point2, current_pos):
        # 绘制圆形边界
        circle = Circle((0, 0), self.radius, fill=False, color='gray', linestyle='--')
        self.ax1.add_patch(circle)
        
        # 绘制圆心（用户位置）
        self.ax1.plot(0, 0, 'ro', markersize=10, label='用户位置')
        
        # 绘制起始点和终点
        self.ax1.plot(point1[0], point1[1], 'bo', markersize=8, label='点1')
        self.ax1.plot(point2[0], point2[1], 'go', markersize=8, label='点2')
        
        # 绘制连接线
        self.ax1.plot([point1[0], point2[0]], [point1[1], point2[1]], 'k-', linewidth=2)
        
        # 绘制当前无人机位置
        self.ax1.plot(current_pos[0], current_pos[1], 'o', color='orange', markersize=10, label='无人机')
        
        # 绘制从圆心到无人机的连线
        self.ax1.plot([0, current_pos[0]], [0, current_pos[1]], 'r--', alpha=0.7)
        
        # 设置坐标轴
        limit = self.radius * 1.2
        self.ax1.set_xlim(-limit, limit)
        self.ax1.set_ylim(-limit, limit)
        self.ax1.set_aspect('equal')
        self.ax1.grid(True, alpha=0.3)
        self.ax1.set_xlabel('距离 (m)')
        self.ax1.set_ylabel('距离 (m)')
        self.ax1.set_title('无人机飞行路径')
        self.ax1.legend()
        
    def plot_transmission_rates(self, rates):
        # 绘制传输速率柱状图
        categories = ['上行速率', '下行速率']
        values = [rates['uplink_rate']/1e6, rates['downlink_rate']/1e6]
        colors = ['blue', 'red']
        
        bars = self.ax2.bar(categories, values, color=colors, alpha=0.7)
        self.ax2.set_ylabel('速率 (Mbps)')
        self.ax2.set_title('传输速率')
        self.ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            self.ax2.text(bar.get_x() + bar.get_width()/2., height,
                         f'{value:.2f}', ha='center', va='bottom')
                         
    def plot_parameters(self):
        # 显示当前参数
        param_text = f'半径: {self.radius}m\n'
        param_text += f'角度1: {self.angle1}°\n'
        param_text += f'角度2: {self.angle2}°\n'
        param_text += f'高度: {self.uav_height}m\n'
        param_text += f'时间: {self.time_progress:.2f}'
        
        self.ax3.text(0.5, 0.5, param_text, ha='center', va='center', 
                     transform=self.ax3.transAxes, fontsize=12,
                     bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        self.ax3.set_title('当前参数')
        self.ax3.set_xlim(0, 1)
        self.ax3.set_ylim(0, 1)
        
    def plot_path_loss(self, rates):
        # 显示路径损耗信息
        loss_text = f'路径损耗: {rates["path_loss_db"]:.1f} dB\n'
        loss_text += f'LoS概率: {rates["los_probability"]:.3f}\n'
        loss_text += f'俯仰角: {rates["pitch_angle"]:.1f}°'
        
        self.ax4.text(0.5, 0.5, loss_text, ha='center', va='center',
                     transform=self.ax4.transAxes, fontsize=12,
                     bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
        self.ax4.set_title('路径损耗分析')
        self.ax4.set_xlim(0, 1)
        self.ax4.set_ylim(0, 1)
        
    def on_main_window_close(self):
        """主窗口关闭时的处理"""
        if hasattr(self, 'control_window'):
            self.control_window.destroy()
        self.root.destroy()
        
    def on_control_window_close(self):
        """控制窗口关闭时的处理"""
        self.control_window.withdraw()  # 隐藏而不是销毁
        
    def run(self):
        print("="*60)
        print("无人机传输速率可视化系统 - 分离窗口版")
        print("="*60)
        print("✅ 图形窗口：显示实时图表和数据可视化")
        print("✅ 控制面板：独立窗口，调节所有参数")
        print("✅ 实时同步：控制面板的改变立即反映在图形中")
        print("="*60)
        self.root.mainloop()

if __name__ == "__main__":
    visualizer = UAVTransmissionVisualizer()
    visualizer.run()
